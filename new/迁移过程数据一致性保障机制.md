# 迁移过程中的数据一致性保障机制

## 一、核心挑战：双写与迁移的竞态冲突

### 1.1 双写前历史数据的UPDATE冲突问题

**问题描述：**
在双写策略启动后，系统会同时向新旧两张表写入数据。但对于双写开始前就存在的历史数据，当业务发起UPDATE操作时，会出现以下问题：

- **旧表UPDATE成功**：历史数据在旧表中存在，UPDATE操作正常执行
- **新表UPDATE失败**：由于该数据尚未被迁移脚本同步到新表，UPDATE操作影响行数为0
- **数据状态不一致**：旧表数据被更新，新表数据不存在，造成严重的数据不一致

**典型场景：**
```sql
-- 双写开始前：订单ID=12345只存在于旧表
-- 双写开始后：业务发起状态更新
UPDATE t_trans_order SET status = 'COMPLETED' WHERE id = '12345';

-- 结果：
-- 旧表：UPDATE成功，status = 'COMPLETED'  
-- 新表：UPDATE失败，影响行数 = 0，数据不存在
```

### 1.2 迁移脚本与增量双写的竞态条件问题

**问题描述：**
在迁移脚本执行过程中，与增量双写操作存在微妙的时间窗口竞态：

1. **迁移脚本读取**：从旧表读取一批数据（如1000条）
2. **增量更新发生**：在迁移脚本写入新表前，业务发起了对其中某条数据的UPDATE
3. **迁移脚本写入**：迁移脚本将读取到的"旧状态"数据写入新表
4. **数据被覆盖**：增量更新的"新状态"被迁移脚本的"旧状态"覆盖

**时序图示例：**
```
时间线：  T1        T2        T3        T4
迁移脚本：[读取旧表] -------- [写入新表] --------
增量双写：-------- [UPDATE] -------- [被覆盖]
结果：    旧状态    新状态    旧状态    数据不一致
```

## 二、冲突检测与记录机制

### 2.1 冲突表设计与建设

为了系统性地管理和解决上述冲突，我们设计专门的冲突记录表：

```sql
-- 迁移冲突记录表
CREATE TABLE `migration_conflicts` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
  `business_id` VARCHAR(64) NOT NULL COMMENT '业务数据ID',
  `table_name` VARCHAR(64) NOT NULL COMMENT '涉及的表名',
  `conflict_type` VARCHAR(32) NOT NULL COMMENT '冲突类型：UPDATE_MISS|RACE_CONDITION',
  `conflict_source` VARCHAR(32) NOT NULL COMMENT '冲突来源：DUAL_WRITE|MIGRATION',
  `old_data_hash` VARCHAR(64) COMMENT '旧表数据哈希值',
  `new_data_hash` VARCHAR(64) COMMENT '新表数据哈希值',
  `conflict_detail` TEXT COMMENT '冲突详情JSON',
  `status` VARCHAR(16) DEFAULT 'PENDING' COMMENT '处理状态：PENDING|RESOLVED|FAILED',
  `retry_count` INT DEFAULT 0 COMMENT '重试次数',
  `create_time` TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `resolve_time` TIMESTAMP NULL COMMENT '解决时间',
  
  INDEX `idx_business_id` (`business_id`),
  INDEX `idx_conflict_type` (`conflict_type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB COMMENT='迁移过程冲突记录表';
```

### 2.2 冲突记录的触发时机

#### 2.2.1 双写UPDATE冲突记录

在双写策略的UPDATE操作中，当新表更新影响行数为0时触发：

```java
/**
 * 双写UPDATE操作处理
 */
public class DualWriteUpdateHandler {
    
    public void handleUpdate(QueryContext context) {
        // 1. 先更新旧表
        int oldTableRows = updateOldTable(context);
        
        // 2. 再更新新表
        int newTableRows = updateNewTable(context);
        
        // 3. 检测冲突：新表影响行数为0但旧表成功
        if (oldTableRows > 0 && newTableRows == 0) {
            recordConflict(context.getBusinessId(), "UPDATE_MISS", "DUAL_WRITE");
        }
    }
    
    private void recordConflict(String businessId, String conflictType, String source) {
        MigrationConflict conflict = new MigrationConflict();
        conflict.setBusinessId(businessId);
        conflict.setConflictType(conflictType);
        conflict.setConflictSource(source);
        conflict.setStatus("PENDING");
        
        conflictDao.insert(conflict);
    }
}
```

#### 2.2.2 迁移过程竞态冲突记录

在迁移脚本的数据比对过程中，发现内容不一致时触发：

```java
/**
 * 迁移窗口处理器
 */
public class MigrationWindowProcessor {
    
    public void processWindow(MigrationWindow window) {
        // 1. 从旧表读取数据
        List<OrderEntity> oldData = readFromOldTable(window);
        
        // 2. 批量插入新表
        batchInsertToNewTable(oldData);
        
        // 3. 从新表回查数据
        List<OrderEntity> newData = readFromNewTable(window);
        
        // 4. 数量校验
        if (oldData.size() != newData.size()) {
            throw new MigrationException("数量不一致，迁移失败");
        }
        
        // 5. 内容逐一比对
        for (int i = 0; i < oldData.size(); i++) {
            OrderEntity oldEntity = oldData.get(i);
            OrderEntity newEntity = newData.get(i);
            
            if (!dataEquals(oldEntity, newEntity)) {
                // 记录竞态冲突
                recordConflict(oldEntity.getId(), "RACE_CONDITION", "MIGRATION");
            }
        }
    }
}
```

### 2.3 冲突数据的分类与标识

**冲突类型分类：**

1. **UPDATE_MISS**：双写UPDATE未命中新表
   - 原因：数据尚未迁移到新表
   - 特征：旧表UPDATE成功，新表影响行数为0
   - 优先级：高（影响业务数据一致性）

2. **RACE_CONDITION**：迁移与双写竞态
   - 原因：迁移过程中发生增量更新
   - 特征：迁移前后数据内容不一致
   - 优先级：中（可通过后续修复解决）

## 三、冲突解决与数据修复

### 3.1 冲突数据的批量修复流程

迁移完成后，通过专门的修复脚本处理所有冲突数据：

```java
/**
 * 冲突数据修复器
 */
@Component
public class ConflictDataResolver {
    
    /**
     * 批量修复冲突数据
     */
    public void resolveConflicts() {
        int batchSize = 100;
        int offset = 0;
        
        while (true) {
            // 分批读取待处理冲突
            List<MigrationConflict> conflicts = conflictDao.findPendingConflicts(offset, batchSize);
            
            if (conflicts.isEmpty()) {
                break;
            }
            
            // 批量处理冲突
            for (MigrationConflict conflict : conflicts) {
                try {
                    resolveConflict(conflict);
                } catch (Exception e) {
                    handleResolveFailure(conflict, e);
                }
            }
            
            offset += batchSize;
        }
    }
    
    /**
     * 解决单个冲突
     */
    private void resolveConflict(MigrationConflict conflict) {
        String businessId = conflict.getBusinessId();
        
        // 1. 从旧表读取权威数据
        OrderEntity authoritativeData = oldTableDao.findById(businessId);
        
        if (authoritativeData == null) {
            // 数据已被删除，标记冲突为已解决
            conflict.setStatus("RESOLVED");
            conflict.setResolveTime(new Date());
            conflictDao.update(conflict);
            return;
        }
        
        // 2. 强制更新新表数据
        newTableDao.forceUpdate(authoritativeData);
        
        // 3. 验证修复结果
        OrderEntity newData = newTableDao.findById(businessId);
        if (dataEquals(authoritativeData, newData)) {
            conflict.setStatus("RESOLVED");
            conflict.setResolveTime(new Date());
        } else {
            conflict.setStatus("FAILED");
            conflict.setRetryCount(conflict.getRetryCount() + 1);
        }
        
        conflictDao.update(conflict);
    }
}
```

### 3.2 基于老表权威数据的强制更新策略

**核心原则：旧表数据为绝对权威**

在冲突解决过程中，我们始终以旧表数据为准，无条件覆盖新表数据：

```java
/**
 * 强制更新策略
 */
public class ForceUpdateStrategy {
    
    /**
     * 强制更新新表数据
     * @param authoritativeData 来自旧表的权威数据
     */
    public void forceUpdate(OrderEntity authoritativeData) {
        // 使用REPLACE INTO确保数据一致性
        String sql = """
            REPLACE INTO t_trans_order_${shard} 
            (id, cust_no, order_status, amount, create_time, update_time, ...) 
            VALUES (?, ?, ?, ?, ?, ?, ...)
            """;
        
        // 计算分片
        int shardIndex = calculateShard(authoritativeData.getId());
        String actualSql = sql.replace("${shard}", String.valueOf(shardIndex));
        
        // 执行强制更新
        jdbcTemplate.update(actualSql, 
            authoritativeData.getId(),
            authoritativeData.getCustNo(),
            authoritativeData.getOrderStatus(),
            authoritativeData.getAmount(),
            authoritativeData.getCreateTime(),
            authoritativeData.getUpdateTime()
            // ... 其他字段
        );
    }
}
```

### 3.3 修复过程的幂等性保障

**幂等性设计原则：**

1. **可重复执行**：修复脚本可以多次运行，不会产生副作用
2. **状态追踪**：通过冲突表状态字段追踪处理进度
3. **断点续传**：支持从中断点继续执行

```java
/**
 * 幂等性修复控制器
 */
public class IdempotentResolver {
    
    /**
     * 幂等性修复入口
     */
    public void idempotentResolve() {
        // 1. 只处理PENDING状态的冲突
        List<MigrationConflict> pendingConflicts = 
            conflictDao.findByStatus("PENDING");
        
        // 2. 对于FAILED状态且重试次数未超限的冲突，重新处理
        List<MigrationConflict> retryableConflicts = 
            conflictDao.findRetryableConflicts(MAX_RETRY_COUNT);
        
        // 3. 合并处理列表
        List<MigrationConflict> allConflicts = new ArrayList<>();
        allConflicts.addAll(pendingConflicts);
        allConflicts.addAll(retryableConflicts);
        
        // 4. 批量处理
        processConflicts(allConflicts);
    }
}
```

## 四、多重校验保障机制

### 4.1 并行校验脚本设计

在迁移过程中，同时运行独立的校验脚本，对已完成迁移的数据进行验证：

```java
/**
 * 并行校验脚本
 */
@Component
public class ParallelValidationScript {
    
    /**
     * 校验指定月份的数据
     * @param targetMonth 目标月份（格式：2024-04）
     */
    public ValidationResult validateMonth(String targetMonth) {
        ValidationResult result = new ValidationResult();
        
        // 1. 获取该月份的数据范围
        DateRange dateRange = getMonthDateRange(targetMonth);
        
        // 2. 分批校验数据
        int batchSize = 1000;
        long offset = 0;
        
        while (true) {
            List<String> businessIds = getBusinessIdsByDateRange(dateRange, offset, batchSize);
            
            if (businessIds.isEmpty()) {
                break;
            }
            
            // 3. 批量比对数据
            BatchCompareResult batchResult = batchCompareData(businessIds);
            result.merge(batchResult);
            
            offset += batchSize;
        }
        
        return result;
    }
    
    /**
     * 批量比对数据
     */
    private BatchCompareResult batchCompareData(List<String> businessIds) {
        BatchCompareResult result = new BatchCompareResult();
        
        // 1. 从旧表批量读取
        Map<String, OrderEntity> oldDataMap = oldTableDao.batchFind(businessIds);
        
        // 2. 从新表批量读取
        Map<String, OrderEntity> newDataMap = newTableDao.batchFind(businessIds);
        
        // 3. 数量比对
        result.setOldCount(oldDataMap.size());
        result.setNewCount(newDataMap.size());
        
        // 4. 内容逐一比对
        for (String businessId : businessIds) {
            OrderEntity oldData = oldDataMap.get(businessId);
            OrderEntity newData = newDataMap.get(businessId);
            
            if (oldData == null && newData == null) {
                continue; // 都不存在，正常
            }
            
            if (oldData == null || newData == null) {
                result.addMissingData(businessId);
                continue;
            }
            
            if (!dataEquals(oldData, newData)) {
                result.addInconsistentData(businessId);
            }
        }
        
        return result;
    }
}
```

### 4.2 滞后校验策略（N-1月数据校验）

**策略说明：**
在迁移N月数据时，同时校验N-1月的数据，确保已迁移数据的持续一致性。

```java
/**
 * 滞后校验调度器
 */
@Component
public class LaggedValidationScheduler {
    
    /**
     * 根据当前迁移进度，自动校验上一个月的数据
     */
    @Scheduled(fixedDelay = 3600000) // 每小时执行一次
    public void autoValidateLastMonth() {
        // 1. 获取当前迁移进度
        String currentMigrationMonth = migrationProgressService.getCurrentMonth();
        
        // 2. 计算上一个月
        String lastMonth = calculateLastMonth(currentMigrationMonth);
        
        // 3. 检查是否已校验过
        if (validationRecordService.isValidated(lastMonth)) {
            return;
        }
        
        // 4. 执行校验
        ValidationResult result = parallelValidationScript.validateMonth(lastMonth);
        
        // 5. 记录校验结果
        validationRecordService.recordValidation(lastMonth, result);
        
        // 6. 如果发现问题，发送告警
        if (result.hasIssues()) {
            alertService.sendValidationAlert(lastMonth, result);
        }
    }
}
```

### 4.3 校验结果的监控与告警

**监控指标设计：**

```java
/**
 * 校验结果监控
 */
@Component
public class ValidationMonitor {
    
    /**
     * 校验结果指标
     */
    public static class ValidationMetrics {
        private long totalRecords;        // 总记录数
        private long consistentRecords;   // 一致记录数
        private long inconsistentRecords; // 不一致记录数
        private long missingRecords;      // 缺失记录数
        private double consistencyRate;   // 一致性比率
        
        // getters and setters...
    }
    
    /**
     * 发送校验告警
     */
    public void sendValidationAlert(String month, ValidationResult result) {
        ValidationMetrics metrics = calculateMetrics(result);
        
        // 1. 一致性比率低于阈值时告警
        if (metrics.getConsistencyRate() < 0.999) { // 99.9%
            AlertMessage alert = new AlertMessage()
                .setLevel(AlertLevel.HIGH)
                .setTitle("数据一致性校验异常")
                .setContent(String.format(
                    "月份：%s\n" +
                    "总记录数：%d\n" +
                    "一致记录数：%d\n" +
                    "不一致记录数：%d\n" +
                    "缺失记录数：%d\n" +
                    "一致性比率：%.4f%%",
                    month,
                    metrics.getTotalRecords(),
                    metrics.getConsistentRecords(),
                    metrics.getInconsistentRecords(),
                    metrics.getMissingRecords(),
                    metrics.getConsistencyRate() * 100
                ));
            
            alertService.send(alert);
        }
        
        // 2. 记录监控指标
        monitoringService.recordMetrics("validation.consistency_rate", 
            metrics.getConsistencyRate(), 
            Tags.of("month", month));
    }
}
```

## 五、完整的一致性保障流程

### 5.1 迁移阶段的一致性策略

**阶段划分与策略：**

1. **准备阶段**
   - 创建冲突记录表
   - 部署双写策略
   - 启动冲突检测机制

2. **迁移阶段**
   - 执行存量数据迁移
   - 实时记录冲突数据
   - 并行执行滞后校验

3. **修复阶段**
   - 批量处理冲突数据
   - 执行最终一致性校验
   - 生成迁移报告

### 5.2 校验阶段的一致性验证

**多层次校验体系：**

```java
/**
 * 多层次校验执行器
 */
@Component
public class MultiLevelValidator {
    
    /**
     * 执行完整的多层次校验
     */
    public ComprehensiveValidationResult executeFullValidation() {
        ComprehensiveValidationResult result = new ComprehensiveValidationResult();
        
        // 第一层：数量校验
        CountValidationResult countResult = executeCountValidation();
        result.setCountValidation(countResult);
        
        // 第二层：抽样内容校验
        SampleValidationResult sampleResult = executeSampleValidation();
        result.setSampleValidation(sampleResult);
        
        // 第三层：全量内容校验（可选）
        if (countResult.isConsistent() && sampleResult.getConsistencyRate() > 0.99) {
            FullValidationResult fullResult = executeFullValidation();
            result.setFullValidation(fullResult);
        }
        
        return result;
    }
}
```

### 5.3 上线前的最终确认机制

**上线前检查清单：**

```java
/**
 * 上线前最终检查
 */
@Component
public class PreLaunchChecker {
    
    /**
     * 执行上线前检查
     */
    public PreLaunchCheckResult executePreLaunchCheck() {
        PreLaunchCheckResult result = new PreLaunchCheckResult();
        
        // 1. 冲突数据检查
        long pendingConflicts = conflictDao.countPendingConflicts();
        result.setPendingConflicts(pendingConflicts);
        
        // 2. 数据一致性检查
        ValidationResult consistencyCheck = executeConsistencyCheck();
        result.setConsistencyCheck(consistencyCheck);
        
        // 3. 性能基准测试
        PerformanceTestResult perfTest = executePerformanceTest();
        result.setPerformanceTest(perfTest);
        
        // 4. 综合评估
        boolean readyForLaunch = evaluateReadiness(result);
        result.setReadyForLaunch(readyForLaunch);
        
        return result;
    }
    
    /**
     * 评估是否准备好上线
     */
    private boolean evaluateReadiness(PreLaunchCheckResult result) {
        return result.getPendingConflicts() == 0 
            && result.getConsistencyCheck().getConsistencyRate() >= 0.9999
            && result.getPerformanceTest().isAcceptable();
    }
}
```

通过这套完整的数据一致性保障机制，我们能够：

1. **及时发现问题**：通过实时冲突检测，快速识别数据不一致
2. **系统化解决冲突**：通过分类处理和批量修复，确保数据最终一致
3. **多重验证保障**：通过并行校验和滞后验证，持续监控数据质量
4. **安全上线保证**：通过最终检查机制，确保系统稳定上线

这种设计确保了在复杂的分库分表迁移过程中，数据的完整性和一致性得到全方位保障。
