# 第五章 - 存量数据迁移与校验机制（续）

## 5.3 存量数据迁移工程

### 5.3.1 迁移工具技术选型

在选择存量数据迁移工具时，我们对业界流行的开源数据同步工具DataX与完全自研方案进行了深入对比分析。

**DataX方案评估：开源工具的优势与局限性分析**

DataX是阿里巴巴开源的异构数据源离线同步工具，在业界有着广泛的应用。

*优势分析：*
- **成熟稳定**：经过大量生产环境验证，核心功能稳定可靠
- **高性能处理**：其核心的`splitPk`功能在处理海量单表时，能自动分片、并发抽取，极大提升迁移效率
- **插件丰富**：支持多种数据源类型，包括MySQL、Oracle、PostgreSQL等
- **配置简单**：通过JSON配置文件即可定义迁移任务

*劣势与挑战：*
1. **复杂转换能力缺失**：DataX被设计为纯粹的数据搬运工，其转换（Transform）能力很弱。对于我们最复杂的"大字段归档"场景，它无法原生支持需要聚合的逻辑
2. **写入逻辑不匹配**：其标准的`mysqlwriter`插件只能向单张目标表写入，不具备根据源数据计算并写入到不同分片表（`t_order_N`）的能力
3. **流程编排笨重**：对于"按月归档"场景，需要外部脚本循环调用DataX作业，整个流程的原子性和状态管理会变得非常复杂
4. **性能调优不可控**：黑盒特性使得无法精准地控制读写批次、并发数和限流，在速度和系统影响间找到最佳平衡

*结论：*
若要使用DataX，我们必须为其开发高度定制的`Writer`插件，并用外部脚本包裹复杂的`Reader`和编排逻辑。这不仅开发成本高，也违背了使用成熟工具简化问题的初衷。

**自研脚本优势：完全可控的迁移工具设计**

*优势分析：*
- **逻辑完全可控**：尤其是处理"大字段归档"这种需要应用层聚合的复杂逻辑，自己写的代码最清晰、最可靠
- **错误处理精细化**：我们可以定义任意粒度的错误处理、重试和死信队列机制
- **性能调优灵活**：可以精准地控制读写批次、并发数和限流，在速度和系统影响间找到最佳平衡
- **无外部依赖**：整个迁移方案的技术栈与我们现有应用保持一致，降低了运维复杂度
- **定制化程度高**：可以根据具体业务需求进行深度定制

*劣势：*
- 需要投入一定的开发资源，从零构建
- 需要自行处理各种边界情况和异常场景

**最终决策依据：稳定性与可控性的权衡**

基于以上对比，为了确保迁移过程的绝对稳定与可控，我们最终决定**不依赖任何第三方数据同步工具，而是自主研发一个独立的、高可用的、可配置的Java迁移应用程序**。

### 5.3.2 自研迁移脚本设计

我们设计的迁移脚本采用模块化架构，具备高度的可配置性和扩展性：

**分批处理机制：大表数据的批量处理策略**

```java
/**
 * 分批处理管理器
 * 负责将大表数据分解为可管理的小批次进行处理
 */
@Component
public class BatchProcessingManager {
    
    private static final Logger log = LoggerFactory.getLogger(BatchProcessingManager.class);
    
    /** 默认批次大小 */
    @Value("${migration.batch.size:10000}")
    private int defaultBatchSize;
    
    /** 最大批次大小 */
    @Value("${migration.batch.max-size:50000}")
    private int maxBatchSize;
    
    /** 动态批次调整器 */
    @Autowired
    private IBatchSizeAdjuster batchSizeAdjuster;
    
    /**
     * 创建批次处理器
     * @param tableName 表名
     * @param startId 起始ID
     * @param endId 结束ID
     * @return 批次处理器
     */
    public BatchProcessor createBatchProcessor(String tableName, Long startId, Long endId) {
        // 计算总数据量
        long totalCount = endId - startId + 1;
        
        // 动态调整批次大小
        int batchSize = batchSizeAdjuster.adjustBatchSize(tableName, totalCount, defaultBatchSize);
        batchSize = Math.min(batchSize, maxBatchSize);
        
        log.info("创建批次处理器: table={}, startId={}, endId={}, totalCount={}, batchSize={}", 
                tableName, startId, endId, totalCount, batchSize);
        
        return new BatchProcessor(tableName, startId, endId, batchSize);
    }
    
    /**
     * 批次处理器内部类
     */
    public static class BatchProcessor {
        private final String tableName;
        private final Long startId;
        private final Long endId;
        private final int batchSize;
        private Long currentId;
        
        public BatchProcessor(String tableName, Long startId, Long endId, int batchSize) {
            this.tableName = tableName;
            this.startId = startId;
            this.endId = endId;
            this.batchSize = batchSize;
            this.currentId = startId;
        }
        
        /**
         * 是否还有下一批次
         */
        public boolean hasNext() {
            return currentId <= endId;
        }
        
        /**
         * 获取下一批次的范围
         */
        public BatchRange getNext() {
            if (!hasNext()) {
                return null;
            }
            
            Long batchStartId = currentId;
            Long batchEndId = Math.min(currentId + batchSize - 1, endId);
            
            currentId = batchEndId + 1;
            
            return new BatchRange(batchStartId, batchEndId);
        }
        
        /**
         * 获取处理进度
         */
        public double getProgress() {
            if (currentId > endId) {
                return 1.0;
            }
            return (double) (currentId - startId) / (endId - startId + 1);
        }
    }
    
    /**
     * 批次范围数据结构
     */
    public static class BatchRange {
        private final Long startId;
        private final Long endId;
        
        public BatchRange(Long startId, Long endId) {
            this.startId = startId;
            this.endId = endId;
        }
        
        public Long getStartId() { return startId; }
        public Long getEndId() { return endId; }
        public long getCount() { return endId - startId + 1; }
        
        @Override
        public String toString() {
            return String.format("[%d, %d] (count=%d)", startId, endId, getCount());
        }
    }
}
```

**断点续传功能：迁移中断后的恢复机制**

```java
/**
 * 断点续传管理器
 * 支持迁移任务的中断恢复功能
 */
@Component
public class CheckpointManager {
    
    private static final Logger log = LoggerFactory.getLogger(CheckpointManager.class);
    
    /** 检查点DAO */
    @Autowired
    private ICheckpointDao checkpointDao;
    
    /** 检查点保存间隔（处理多少批次后保存一次） */
    @Value("${migration.checkpoint.save-interval:10}")
    private int saveInterval;
    
    /**
     * 获取或创建检查点
     * @param taskId 任务ID
     * @param tableName 表名
     * @param totalStartId 总的起始ID
     * @param totalEndId 总的结束ID
     * @return 检查点信息
     */
    public CheckpointInfo getOrCreateCheckpoint(String taskId, String tableName, 
                                              Long totalStartId, Long totalEndId) {
        // 尝试从数据库获取现有检查点
        CheckpointInfo checkpoint = checkpointDao.findByTaskId(taskId);
        
        if (checkpoint == null) {
            // 创建新的检查点
            checkpoint = new CheckpointInfo();
            checkpoint.setTaskId(taskId);
            checkpoint.setTableName(tableName);
            checkpoint.setTotalStartId(totalStartId);
            checkpoint.setTotalEndId(totalEndId);
            checkpoint.setCurrentId(totalStartId);
            checkpoint.setStatus(CheckpointStatus.CREATED);
            checkpoint.setCreateTime(new Date());
            checkpoint.setUpdateTime(new Date());
            
            checkpointDao.save(checkpoint);
            
            log.info("创建新检查点: taskId={}, table={}, range=[{}, {}]", 
                    taskId, tableName, totalStartId, totalEndId);
        } else {
            log.info("恢复现有检查点: taskId={}, table={}, currentId={}, progress={:.2f}%", 
                    taskId, tableName, checkpoint.getCurrentId(), 
                    checkpoint.getProgress() * 100);
        }
        
        return checkpoint;
    }
    
    /**
     * 更新检查点
     * @param checkpoint 检查点信息
     * @param processedId 已处理的ID
     * @param batchCount 已处理的批次数
     */
    public void updateCheckpoint(CheckpointInfo checkpoint, Long processedId, int batchCount) {
        checkpoint.setCurrentId(processedId);
        checkpoint.setUpdateTime(new Date());
        
        // 根据保存间隔决定是否持久化
        if (batchCount % saveInterval == 0) {
            checkpointDao.update(checkpoint);
            
            log.debug("保存检查点: taskId={}, currentId={}, progress={:.2f}%", 
                     checkpoint.getTaskId(), processedId, checkpoint.getProgress() * 100);
        }
    }
    
    /**
     * 标记检查点完成
     */
    public void markCheckpointCompleted(CheckpointInfo checkpoint) {
        checkpoint.setStatus(CheckpointStatus.COMPLETED);
        checkpoint.setCurrentId(checkpoint.getTotalEndId());
        checkpoint.setCompleteTime(new Date());
        checkpoint.setUpdateTime(new Date());
        
        checkpointDao.update(checkpoint);
        
        log.info("检查点已完成: taskId={}, table={}", 
                checkpoint.getTaskId(), checkpoint.getTableName());
    }
    
    /**
     * 标记检查点失败
     */
    public void markCheckpointFailed(CheckpointInfo checkpoint, String errorMessage) {
        checkpoint.setStatus(CheckpointStatus.FAILED);
        checkpoint.setErrorMessage(errorMessage);
        checkpoint.setUpdateTime(new Date());
        
        checkpointDao.update(checkpoint);
        
        log.error("检查点失败: taskId={}, error={}", checkpoint.getTaskId(), errorMessage);
    }
}

/**
 * 检查点信息实体
 */
public class CheckpointInfo {
    private String taskId;
    private String tableName;
    private Long totalStartId;
    private Long totalEndId;
    private Long currentId;
    private CheckpointStatus status;
    private String errorMessage;
    private Date createTime;
    private Date updateTime;
    private Date completeTime;
    
    /**
     * 计算处理进度
     */
    public double getProgress() {
        if (totalEndId.equals(totalStartId)) {
            return currentId.equals(totalEndId) ? 1.0 : 0.0;
        }
        return (double) (currentId - totalStartId) / (totalEndId - totalStartId);
    }
    
    /**
     * 判断是否可以恢复
     */
    public boolean canResume() {
        return status == CheckpointStatus.CREATED || 
               status == CheckpointStatus.RUNNING ||
               status == CheckpointStatus.PAUSED;
    }
    
    // getter和setter方法...
}

/**
 * 检查点状态枚举
 */
public enum CheckpointStatus {
    CREATED,    // 已创建
    RUNNING,    // 运行中
    PAUSED,     // 已暂停
    COMPLETED,  // 已完成
    FAILED      // 已失败
}
```

**进度监控体系：迁移进度的实时跟踪**

```java
/**
 * 迁移进度监控器
 * 提供实时的迁移进度跟踪和统计信息
 */
@Component
public class MigrationProgressMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(MigrationProgressMonitor.class);
    
    /** 进度统计缓存 */
    private final Map<String, ProgressStatistics> progressCache = new ConcurrentHashMap<>();
    
    /** 指标注册表 */
    @Autowired
    private MeterRegistry meterRegistry;
    
    /** 进度更新间隔（秒） */
    @Value("${migration.progress.update-interval:30}")
    private int updateInterval;
    
    /**
     * 初始化任务监控
     */
    public void initTaskMonitoring(String taskId, String tableName, long totalCount) {
        ProgressStatistics stats = new ProgressStatistics();
        stats.setTaskId(taskId);
        stats.setTableName(tableName);
        stats.setTotalCount(totalCount);
        stats.setStartTime(System.currentTimeMillis());
        
        progressCache.put(taskId, stats);
        
        // 注册Micrometer指标
        Gauge.builder("migration.progress.percentage")
                .description("迁移进度百分比")
                .tags("task_id", taskId, "table", tableName)
                .register(meterRegistry, stats, ProgressStatistics::getProgressPercentage);
        
        Gauge.builder("migration.speed.records_per_second")
                .description("迁移速度（记录/秒）")
                .tags("task_id", taskId, "table", tableName)
                .register(meterRegistry, stats, ProgressStatistics::getRecordsPerSecond);
        
        log.info("初始化任务监控: taskId={}, table={}, totalCount={}", 
                taskId, tableName, totalCount);
    }
    
    /**
     * 更新进度
     */
    public void updateProgress(String taskId, long processedCount, long currentBatchSize) {
        ProgressStatistics stats = progressCache.get(taskId);
        if (stats == null) {
            log.warn("未找到任务统计信息: taskId={}", taskId);
            return;
        }
        
        stats.setProcessedCount(processedCount);
        stats.setLastUpdateTime(System.currentTimeMillis());
        stats.incrementBatchCount();
        stats.addBatchSize(currentBatchSize);
        
        // 定期输出进度日志
        if (stats.getBatchCount() % 10 == 0) {
            logProgress(stats);
        }
    }
    
    /**
     * 记录错误
     */
    public void recordError(String taskId, String errorType, String errorMessage) {
        ProgressStatistics stats = progressCache.get(taskId);
        if (stats != null) {
            stats.incrementErrorCount();
            stats.setLastError(errorMessage);
        }
        
        // 记录错误指标
        Counter.builder("migration.errors.total")
                .description("迁移错误总数")
                .tags("task_id", taskId, "error_type", errorType)
                .register(meterRegistry)
                .increment();
        
        log.error("迁移错误: taskId={}, errorType={}, message={}", 
                 taskId, errorType, errorMessage);
    }
    
    /**
     * 完成任务监控
     */
    public void completeTaskMonitoring(String taskId) {
        ProgressStatistics stats = progressCache.get(taskId);
        if (stats != null) {
            stats.setCompleteTime(System.currentTimeMillis());
            logFinalStatistics(stats);
        }
        
        log.info("任务监控完成: taskId={}", taskId);
    }
    
    /**
     * 输出进度日志
     */
    private void logProgress(ProgressStatistics stats) {
        log.info("迁移进度: taskId={}, table={}, progress={:.2f}%, speed={:.0f} records/s, " +
                "processed={}/{}, batches={}, errors={}", 
                stats.getTaskId(),
                stats.getTableName(),
                stats.getProgressPercentage(),
                stats.getRecordsPerSecond(),
                stats.getProcessedCount(),
                stats.getTotalCount(),
                stats.getBatchCount(),
                stats.getErrorCount());
    }
    
    /**
     * 输出最终统计信息
     */
    private void logFinalStatistics(ProgressStatistics stats) {
        long totalTime = stats.getCompleteTime() - stats.getStartTime();
        double avgSpeed = stats.getProcessedCount() * 1000.0 / totalTime;
        
        log.info("迁移完成统计: taskId={}, table={}, totalTime={}ms, avgSpeed={:.0f} records/s, " +
                "totalProcessed={}, totalBatches={}, totalErrors={}", 
                stats.getTaskId(),
                stats.getTableName(),
                totalTime,
                avgSpeed,
                stats.getProcessedCount(),
                stats.getBatchCount(),
                stats.getErrorCount());
    }
    
    /**
     * 获取任务进度
     */
    public ProgressStatistics getTaskProgress(String taskId) {
        return progressCache.get(taskId);
    }
    
    /**
     * 获取所有任务进度
     */
    public Map<String, ProgressStatistics> getAllTaskProgress() {
        return new HashMap<>(progressCache);
    }
}

/**
 * 进度统计信息
 */
public class ProgressStatistics {
    private String taskId;
    private String tableName;
    private long totalCount;
    private long processedCount;
    private long startTime;
    private long lastUpdateTime;
    private long completeTime;
    private int batchCount;
    private long totalBatchSize;
    private int errorCount;
    private String lastError;

    /**
     * 计算进度百分比
     */
    public double getProgressPercentage() {
        if (totalCount == 0) {
            return 0.0;
        }
        return (double) processedCount / totalCount * 100.0;
    }

    /**
     * 计算处理速度（记录/秒）
     */
    public double getRecordsPerSecond() {
        long currentTime = lastUpdateTime > 0 ? lastUpdateTime : System.currentTimeMillis();
        long elapsedTime = currentTime - startTime;

        if (elapsedTime <= 0) {
            return 0.0;
        }

        return processedCount * 1000.0 / elapsedTime;
    }

    /**
     * 计算平均批次大小
     */
    public double getAverageBatchSize() {
        if (batchCount == 0) {
            return 0.0;
        }
        return (double) totalBatchSize / batchCount;
    }

    /**
     * 增加批次计数
     */
    public void incrementBatchCount() {
        this.batchCount++;
    }

    /**
     * 增加批次大小统计
     */
    public void addBatchSize(long batchSize) {
        this.totalBatchSize += batchSize;
    }

    /**
     * 增加错误计数
     */
    public void incrementErrorCount() {
        this.errorCount++;
    }

    // getter和setter方法...
    public String getTaskId() { return taskId; }
    public void setTaskId(String taskId) { this.taskId = taskId; }

    public String getTableName() { return tableName; }
    public void setTableName(String tableName) { this.tableName = tableName; }

    public long getTotalCount() { return totalCount; }
    public void setTotalCount(long totalCount) { this.totalCount = totalCount; }

    public long getProcessedCount() { return processedCount; }
    public void setProcessedCount(long processedCount) { this.processedCount = processedCount; }

    public long getStartTime() { return startTime; }
    public void setStartTime(long startTime) { this.startTime = startTime; }

    public long getLastUpdateTime() { return lastUpdateTime; }
    public void setLastUpdateTime(long lastUpdateTime) { this.lastUpdateTime = lastUpdateTime; }

    public long getCompleteTime() { return completeTime; }
    public void setCompleteTime(long completeTime) { this.completeTime = completeTime; }

    public int getBatchCount() { return batchCount; }
    public int getErrorCount() { return errorCount; }

    public String getLastError() { return lastError; }
    public void setLastError(String lastError) { this.lastError = lastError; }
}

**资源控制策略：CPU、内存、网络资源的合理使用**

```java
/**
 * 资源控制管理器
 * 控制迁移过程中的资源使用，避免对生产系统造成影响
 */
@Component
public class ResourceControlManager {

    private static final Logger log = LoggerFactory.getLogger(ResourceControlManager.class);

    /** CPU使用率阈值 */
    @Value("${migration.resource.cpu.threshold:70.0}")
    private double cpuThreshold;

    /** 内存使用率阈值 */
    @Value("${migration.resource.memory.threshold:80.0}")
    private double memoryThreshold;

    /** 数据库连接池使用率阈值 */
    @Value("${migration.resource.db.connection.threshold:70.0}")
    private double dbConnectionThreshold;

    /** 资源检查间隔（毫秒） */
    @Value("${migration.resource.check.interval:5000}")
    private long checkInterval;

    /** 系统监控器 */
    @Autowired
    private ISystemMonitor systemMonitor;

    /** 数据库连接池监控器 */
    @Autowired
    private IConnectionPoolMonitor connectionPoolMonitor;

    /** 限流器 */
    private final RateLimiter rateLimiter;

    /** 资源状态 */
    private volatile ResourceStatus resourceStatus = ResourceStatus.NORMAL;

    public ResourceControlManager() {
        // 初始化限流器，默认每秒允许1000个操作
        this.rateLimiter = RateLimiter.create(1000.0);
    }

    /**
     * 检查资源状态并进行控制
     * @return 是否允许继续执行
     */
    public boolean checkAndControl() {
        // 获取系统资源使用情况
        SystemResourceInfo resourceInfo = systemMonitor.getResourceInfo();

        // 检查CPU使用率
        if (resourceInfo.getCpuUsage() > cpuThreshold) {
            handleHighCpuUsage(resourceInfo.getCpuUsage());
            return false;
        }

        // 检查内存使用率
        if (resourceInfo.getMemoryUsage() > memoryThreshold) {
            handleHighMemoryUsage(resourceInfo.getMemoryUsage());
            return false;
        }

        // 检查数据库连接池使用率
        double dbUsage = connectionPoolMonitor.getUsagePercentage();
        if (dbUsage > dbConnectionThreshold) {
            handleHighDbUsage(dbUsage);
            return false;
        }

        // 资源正常，恢复正常状态
        if (resourceStatus != ResourceStatus.NORMAL) {
            resourceStatus = ResourceStatus.NORMAL;
            log.info("系统资源恢复正常，继续迁移");
        }

        return true;
    }

    /**
     * 处理高CPU使用率
     */
    private void handleHighCpuUsage(double cpuUsage) {
        if (resourceStatus != ResourceStatus.HIGH_CPU) {
            resourceStatus = ResourceStatus.HIGH_CPU;
            log.warn("CPU使用率过高: {:.2f}%, 暂停迁移", cpuUsage);
        }

        // 动态调整限流器速率
        double newRate = rateLimiter.getRate() * 0.8;
        rateLimiter.setRate(Math.max(newRate, 10.0)); // 最低10 ops/s

        // 等待CPU使用率降低
        sleepWithInterruption(checkInterval);
    }

    /**
     * 处理高内存使用率
     */
    private void handleHighMemoryUsage(double memoryUsage) {
        if (resourceStatus != ResourceStatus.HIGH_MEMORY) {
            resourceStatus = ResourceStatus.HIGH_MEMORY;
            log.warn("内存使用率过高: {:.2f}%, 暂停迁移", memoryUsage);
        }

        // 触发垃圾回收
        System.gc();

        // 等待内存释放
        sleepWithInterruption(checkInterval);
    }

    /**
     * 处理高数据库连接使用率
     */
    private void handleHighDbUsage(double dbUsage) {
        if (resourceStatus != ResourceStatus.HIGH_DB) {
            resourceStatus = ResourceStatus.HIGH_DB;
            log.warn("数据库连接使用率过高: {:.2f}%, 暂停迁移", dbUsage);
        }

        // 等待连接释放
        sleepWithInterruption(checkInterval);
    }

    /**
     * 获取限流许可
     */
    public void acquirePermit() {
        rateLimiter.acquire();
    }

    /**
     * 尝试获取限流许可
     */
    public boolean tryAcquirePermit(long timeout, TimeUnit unit) {
        return rateLimiter.tryAcquire(timeout, unit);
    }

    /**
     * 动态调整限流速率
     */
    public void adjustRateLimit(double newRate) {
        rateLimiter.setRate(Math.max(newRate, 1.0));
        log.info("调整限流速率: {} ops/s", newRate);
    }

    /**
     * 获取当前资源状态
     */
    public ResourceStatus getResourceStatus() {
        return resourceStatus;
    }

    /**
     * 可中断的睡眠
     */
    private void sleepWithInterruption(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("资源控制等待被中断");
        }
    }
}

/**
 * 资源状态枚举
 */
public enum ResourceStatus {
    NORMAL,      // 正常
    HIGH_CPU,    // CPU使用率过高
    HIGH_MEMORY, // 内存使用率过高
    HIGH_DB      // 数据库连接使用率过高
}

/**
 * 系统资源信息
 */
public class SystemResourceInfo {
    private double cpuUsage;      // CPU使用率（百分比）
    private double memoryUsage;   // 内存使用率（百分比）
    private long freeMemory;      // 可用内存（字节）
    private long totalMemory;     // 总内存（字节）

    // getter和setter方法...
    public double getCpuUsage() { return cpuUsage; }
    public void setCpuUsage(double cpuUsage) { this.cpuUsage = cpuUsage; }

    public double getMemoryUsage() { return memoryUsage; }
    public void setMemoryUsage(double memoryUsage) { this.memoryUsage = memoryUsage; }

    public long getFreeMemory() { return freeMemory; }
    public void setFreeMemory(long freeMemory) { this.freeMemory = freeMemory; }

    public long getTotalMemory() { return totalMemory; }
    public void setTotalMemory(long totalMemory) { this.totalMemory = totalMemory; }
}
```

### 5.3.3 异构数据源适配

由于历史数据可能存在于不同的表结构中，我们需要设计灵活的适配机制来处理这种异构性。

**策略模式应用：不同数据源结构的统一处理**

```java
/**
 * 数据源读取策略接口
 * 定义了从不同数据源结构读取数据的统一契约
 */
public interface IDataSourceReadStrategy {

    /**
     * 获取策略名称
     */
    String getStrategyName();

    /**
     * 判断是否支持指定的数据源类型
     */
    boolean supports(DataSourceType dataSourceType);

    /**
     * 读取指定范围的数据
     * @param readContext 读取上下文
     * @return 标准化的业务对象列表
     */
    List<StandardBusinessEntity> readData(DataReadContext readContext);

    /**
     * 获取数据总数
     */
    long getTotalCount(DataReadContext readContext);

    /**
     * 获取数据范围（最小ID和最大ID）
     */
    DataRange getDataRange(DataReadContext readContext);
}

**数据源类型识别：巨型单表、按月归档、大字段归档**

```java
/**
 * 数据源类型枚举
 * 定义了系统支持的各种历史数据存储形态
 */
public enum DataSourceType {

    /**
     * 巨型单表
     * 特点：所有历史数据存储在一张大表中，按ID范围读取
     */
    GIANT_SINGLE_TABLE("giant_single_table", "巨型单表"),

    /**
     * 按月归档表
     * 特点：数据按月份分散存储在多张表中，如t_order_202301, t_order_202302
     */
    MONTHLY_ARCHIVE("monthly_archive", "按月归档表"),

    /**
     * 大字段归档表
     * 特点：主表和扩展字段表分离，需要JOIN查询获取完整数据
     */
    LARGE_FIELD_ARCHIVE("large_field_archive", "大字段归档表");

    private final String code;
    private final String description;

    DataSourceType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() { return code; }
    public String getDescription() { return description; }

    /**
     * 根据代码获取数据源类型
     */
    public static DataSourceType fromCode(String code) {
        for (DataSourceType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的数据源类型: " + code);
    }
}

/**
 * 巨型单表读取策略
 * 处理所有数据存储在单张大表中的场景
 */
@Component
public class GiantSingleTableReadStrategy implements IDataSourceReadStrategy {

    private static final Logger log = LoggerFactory.getLogger(GiantSingleTableReadStrategy.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getStrategyName() {
        return "GiantSingleTableRead";
    }

    @Override
    public boolean supports(DataSourceType dataSourceType) {
        return dataSourceType == DataSourceType.GIANT_SINGLE_TABLE;
    }

    @Override
    public List<StandardBusinessEntity> readData(DataReadContext readContext) {
        String tableName = readContext.getSourceTableName();
        Long startId = readContext.getStartId();
        Long endId = readContext.getEndId();

        String sql = String.format(
            "SELECT * FROM %s WHERE id >= ? AND id <= ? ORDER BY id",
            tableName
        );

        log.debug("执行巨型单表查询: table={}, range=[{}, {}]", tableName, startId, endId);

        return jdbcTemplate.query(sql, new Object[]{startId, endId},
                                 new StandardBusinessEntityRowMapper());
    }

    @Override
    public long getTotalCount(DataReadContext readContext) {
        String tableName = readContext.getSourceTableName();
        String sql = String.format("SELECT COUNT(*) FROM %s", tableName);

        Long count = jdbcTemplate.queryForObject(sql, Long.class);
        return count != null ? count : 0L;
    }

    @Override
    public DataRange getDataRange(DataReadContext readContext) {
        String tableName = readContext.getSourceTableName();
        String sql = String.format("SELECT MIN(id), MAX(id) FROM %s", tableName);

        return jdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
            Long minId = rs.getLong(1);
            Long maxId = rs.getLong(2);
            return new DataRange(minId, maxId);
        });
    }
}

/**
 * 按月归档表读取策略
 * 处理数据按月份分散存储的场景
 */
@Component
public class MonthlyArchiveReadStrategy implements IDataSourceReadStrategy {

    private static final Logger log = LoggerFactory.getLogger(MonthlyArchiveReadStrategy.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getStrategyName() {
        return "MonthlyArchiveRead";
    }

    @Override
    public boolean supports(DataSourceType dataSourceType) {
        return dataSourceType == DataSourceType.MONTHLY_ARCHIVE;
    }

    @Override
    public List<StandardBusinessEntity> readData(DataReadContext readContext) {
        List<String> monthlyTables = getMonthlyTables(readContext);
        List<StandardBusinessEntity> allData = new ArrayList<>();

        for (String tableName : monthlyTables) {
            List<StandardBusinessEntity> monthlyData = readMonthlyTable(
                tableName, readContext.getStartId(), readContext.getEndId()
            );
            allData.addAll(monthlyData);

            log.debug("读取月表数据: table={}, count={}", tableName, monthlyData.size());
        }

        // 按ID排序，确保数据有序
        allData.sort(Comparator.comparing(StandardBusinessEntity::getId));

        return allData;
    }

    @Override
    public long getTotalCount(DataReadContext readContext) {
        List<String> monthlyTables = getMonthlyTables(readContext);
        long totalCount = 0;

        for (String tableName : monthlyTables) {
            String sql = String.format("SELECT COUNT(*) FROM %s", tableName);
            Long count = jdbcTemplate.queryForObject(sql, Long.class);
            totalCount += (count != null ? count : 0L);
        }

        return totalCount;
    }

    @Override
    public DataRange getDataRange(DataReadContext readContext) {
        List<String> monthlyTables = getMonthlyTables(readContext);
        Long globalMinId = null;
        Long globalMaxId = null;

        for (String tableName : monthlyTables) {
            String sql = String.format("SELECT MIN(id), MAX(id) FROM %s", tableName);

            DataRange tableRange = jdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
                Long minId = rs.getLong(1);
                Long maxId = rs.getLong(2);
                return new DataRange(minId, maxId);
            });

            if (tableRange != null) {
                if (globalMinId == null || tableRange.getMinId() < globalMinId) {
                    globalMinId = tableRange.getMinId();
                }
                if (globalMaxId == null || tableRange.getMaxId() > globalMaxId) {
                    globalMaxId = tableRange.getMaxId();
                }
            }
        }

        return new DataRange(globalMinId, globalMaxId);
    }

    /**
     * 获取所有月表名称
     */
    private List<String> getMonthlyTables(DataReadContext readContext) {
        String baseTableName = readContext.getSourceTableName();
        String startMonth = readContext.getStartMonth();
        String endMonth = readContext.getEndMonth();

        List<String> tableNames = new ArrayList<>();

        // 生成月表名称列表
        LocalDate start = LocalDate.parse(startMonth + "-01");
        LocalDate end = LocalDate.parse(endMonth + "-01");

        LocalDate current = start;
        while (!current.isAfter(end)) {
            String monthSuffix = current.format(DateTimeFormatter.ofPattern("yyyyMM"));
            String tableName = baseTableName + "_" + monthSuffix;

            // 检查表是否存在
            if (tableExists(tableName)) {
                tableNames.add(tableName);
            }

            current = current.plusMonths(1);
        }

        return tableNames;
    }

    /**
     * 读取单个月表的数据
     */
    private List<StandardBusinessEntity> readMonthlyTable(String tableName, Long startId, Long endId) {
        String sql = String.format(
            "SELECT * FROM %s WHERE id >= ? AND id <= ? ORDER BY id",
            tableName
        );

        return jdbcTemplate.query(sql, new Object[]{startId, endId},
                                 new StandardBusinessEntityRowMapper());
    }

    /**
     * 检查表是否存在
     */
    private boolean tableExists(String tableName) {
        try {
            String sql = "SELECT 1 FROM " + tableName + " LIMIT 1";
            jdbcTemplate.queryForObject(sql, Integer.class);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}

/**
 * 大字段归档表读取策略
 * 处理主表和扩展字段表分离的场景
 */
@Component
public class LargeFieldArchiveReadStrategy implements IDataSourceReadStrategy {

    private static final Logger log = LoggerFactory.getLogger(LargeFieldArchiveReadStrategy.class);

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getStrategyName() {
        return "LargeFieldArchiveRead";
    }

    @Override
    public boolean supports(DataSourceType dataSourceType) {
        return dataSourceType == DataSourceType.LARGE_FIELD_ARCHIVE;
    }

    @Override
    public List<StandardBusinessEntity> readData(DataReadContext readContext) {
        String mainTableName = readContext.getSourceTableName();
        String extTableName = readContext.getExtensionTableName();
        Long startId = readContext.getStartId();
        Long endId = readContext.getEndId();

        // 构建JOIN查询SQL
        String sql = String.format(
            "SELECT m.*, e.large_field_1, e.large_field_2, e.large_field_3 " +
            "FROM %s m LEFT JOIN %s e ON m.id = e.main_id " +
            "WHERE m.id >= ? AND m.id <= ? ORDER BY m.id",
            mainTableName, extTableName
        );

        log.debug("执行大字段归档查询: mainTable={}, extTable={}, range=[{}, {}]",
                 mainTableName, extTableName, startId, endId);

        return jdbcTemplate.query(sql, new Object[]{startId, endId},
                                 new LargeFieldEntityRowMapper());
    }

    @Override
    public long getTotalCount(DataReadContext readContext) {
        String mainTableName = readContext.getSourceTableName();
        String sql = String.format("SELECT COUNT(*) FROM %s", mainTableName);

        Long count = jdbcTemplate.queryForObject(sql, Long.class);
        return count != null ? count : 0L;
    }

    @Override
    public DataRange getDataRange(DataReadContext readContext) {
        String mainTableName = readContext.getSourceTableName();
        String sql = String.format("SELECT MIN(id), MAX(id) FROM %s", mainTableName);

        return jdbcTemplate.queryForObject(sql, (rs, rowNum) -> {
            Long minId = rs.getLong(1);
            Long maxId = rs.getLong(2);
            return new DataRange(minId, maxId);
        });
    }
}
```
```
```
