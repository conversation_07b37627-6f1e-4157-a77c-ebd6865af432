<svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 820 828.3876720246953" width="820" height="828.3876720246953" class="excalidraw-svg"><!-- svg-source:excalidraw --><metadata></metadata><defs><style class="style-fonts">
      @font-face { font-family: Xiaolai; src: url(data:font/woff2;base64,d09GMgABAAAAAATcABEAAAAADSwAAASDAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGhYbHhweBmAANAgKCY4uERAKgziDBAsGAAE2AiQDCAQgBYJeByAMFBvFC8ieA27LDOGplMVgrnkkcj9PKYPnUe79c5MUdl4hfdibhLnbNyZiM7fiSpeuHghPDwZC6Y14JpYdBn3CCjAprFTpqEXoqE+6jHVyN4kSD+hgAUba1hZ9a/U/nH9hq99Pv8z+F7IRDti4vMy/kLExhtQFUCIUSSgUplWyLlNhgCyRrNKVvl/7DrqbEOy/AATwBJC6QpgD3In8TYj6xvp2UGAAugD0zND6gWwFKSg4BJkd289ClriuUeKMFgWiXNnRfXBVF5RtLUMrxLO7AsIrsHe0RI58fw/KSXUCsWtmCGufA3FOuE1CLIxBBlvLZsHarNt9C+BEoPsjzwLYbgKa3oPvCrEZCzK7s1BRT0CDHjUyWQDCIkQzFC9DDgFEVgAinUwWQGxRhNfhSGt3ECMOcTVjXGwETQ2P8SaMU433GueGpCIe2gYziIf2wSRfht32nnGMC5IFkKwhzCYOowiani2mU2Tlf8dZkNo6+A7imObulm5+ab2bL6OwmyQRzV9tHsxfpSJIjJMFkJ0yFTfXMo7zQl6RpZO8YgVuYynSwNz6UjlIwi3c+gguiM1TBQKoqawmwOT1OkwLwjY399gg6UYXCZJBErtJTCULoLR1bd00JZI0BVn1uvFRmBSSGhqhjrxWIevoeg0GGFWDxWSoliAU+7fguWUzwSJZYRPWyNsVsoPu1OCCW3Xp1QNkL8En+YGEP/KkQg7QgxpCCKshvcYAOZYQJ8UjQcRHPlEhJ9GTNaQgVU3RaxoITr8mABpXFKEQXdAAqBCGXTB2ty8AddBxcRglzoOWmkYfyRH34/dd50sPE+4jamvH/UHvGtuuJrUq5rDtzvs7Hjs63iY9sHufgNl3hq0fyB0fGPXY9ZC36rqJFzu7sqCjqCvSTrgPGMENfOe+Pivm8GIrEoeIllz3X+7M81Q+Enes+hSwgW/aIo8CW3MM1r4d8NRjQ1f4EFhCry6zPXqXs/W/723LLg6GHnweoF+ybXdTWkctvWF/0N6XZrUl/oiJZbq80MX2K6GxJ331hR3LbeYV+hHur1YHyA5Q0UFzlndEj/3tU0TiXPXGuNjNdqdogrUlPy9Ympj8esI84o0ey/Mcn68cd+fWjBN2T6AlJEm7YZLs0FaG5n73cLXWgLQ81HR+pVD2QN5Pz1BZYFbZnycr/ewZ/0Ljt46aR5xryS5qiSrtukl68hX9rPfiJufc46H7P5nUa7t101+6/ToAIICOjD/cO/h0lL78r6uH/gTw4n6EAPDWbf/sO2mmXc8BcEUBIPCjKpv/PdZ/O4ngZNA8+hOtbF6pr6RgIcXcT68BDRyKGDFuzXQ+5R4hkGcehTdnPZpMuP2m3+PEXEzsbHwfMBiGYeYzh2EY2qikk3EWsRiG+cwDkUkK6aRTCMU4o4wzlxE4RWSRxOoZpPn1QiLjp0uoZBHi4RTz64j0FQppfj2DnBqj+GxZJDMCw5JT5BA/XgHnVlifZOnIIxfNS3HHfBawMpBhkimW7L920nMR9XRTRRdinERdkUbnLHEZPnywQFWFDAA=); }
      @font-face { font-family: Xiaolai; src: url(data:font/woff2;base64,d09GMgABAAAAAAVMABEAAAAADeAAAATxAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGhYbHhweBmAANAgKCY4uERAKhGyEDgsGAAE2AiQDCAQgBYJeByAMFBtPDMguCmz3FI0Owlk2Rxkm2BimrzfQwfP/du/vc+88KuYk+QLm5YsdXALqpNVH3YcrgVXmT3/q+wFCKUgTICps83eAWA4WCJWiPOY65boGpgxLGUe3HXMJkXWxVik4yBpl9/9b+03cu/NFkUYUL43h7MNKpRTR9xc8iXsTCwVSpLpWmmkoha2N0W03QVzXCYkECOAHkBqFsAjwkjVf+vQrrAQTjN8B3bCAntO1vi7BQKEAmR+HN7KKyTAlTrQ4IJWbSUOsnBpIPXAfD9JsQHgFNqYacvDZPYByqGYgb80cwtrnODaFhxrkwdCk9cB924aV08r7esFB3P2VjQDMwRx2hLUlwrZko/xf28iWrQMWPWVyOAfIRChLzGTSUsUBNjlAFTicA2KibKb9s8trfQmiiObNpHnZSC2RM8MfZzXeS0GTNYjBZYmIwW1hlpp0t6fNIiQ+nAO6hjTGUJKXjTSzpSaL8N8BB2FmAWL6AaW1ZbVsRZ9MXNiHTsIYZbEzIyy/q08mJsQsxk8psuK6cdHlvCDK8QpSRK7AFSyL1CbpplKZL4bjNm3KpL7E3JQLQO2plRwmj9T+WVymRjQXTsGAOy7zxeIg4o5JZjgHyiyoqM3KBG5MmH7d9CHtpMKAlUzKc4LM0wUDIiRddMpaDwWSmvpb8NyqQlAVDbrUUt4QZJNuGbDh6LZVXaB6BF8JEMog5SNBjumJgRSZnlo1B2pBKJUKtaxSvhHklt4Z6DHovVVHkHh6RACNG0UaYttoABQABuAD7oAoqIPJvlBHK8tAOwOD4OjAWG/JCWF6/H6f/2n4zWOS4pCDR98JL01mu9801yaqLt7L7Zocr3y6ue1xMPew7pTqvQcPOqBk/+57+/a9Z/q+lAfVmWOqtMX324bUeEsexFws+Law5hNmXrzNLGm9uD8/6C+LB6bp5doK1XHqpT/tk2xOn7lURKsxr2NYoV8SWlN8fIrD7oTx1ZVm48tX2ZThdtH7pLk2Hcbs8ZqlnLmS4c6GVR2b5y3ZZw/kv5eedqyjt1HZQ/sIu6zbacHMKd384RXJEXzWuepUC1ubxS/56xtw2aOaLIvt4Uaq0HT+iln2yNnExbB75YiqxgNexd3TSV20EWUctHG0cdJ/mmu2o520qfbKyJcDlx+OKfvYVOvrvft+xJm7yZW568HagHFZbEiWj2nd5Va7b01JM4qb+ta3yCi4zW9Gd3FHn4ojh7OkbUhaqEu+NmI4d63QExO5Cnuqp51PoMY4I9m8h+DF+i1b5oqMjv6XSVJ8Wz/orMuRnR90W3vhxXRxnR/iujxXrj2wI7NOZ74v8UVmPZyLFJfDB/tWWMf2+m2OWtdPml/pjPMp8mIfnFaOSzyO6AAAAuis4OGDFUumWHf56vbp9wD3j34tAnhVWXzk/2u73PijFwC4UST3a1dm/OlxNd+A4OSiHvR72rIepT5RiqUUt5K5zmkuatFHDA9QwcM8RohzKUYRpDFGU82m5UZ/jIPl2LHTeD2wEJHQSRsSRCrIoBoFPfQi0kkHiNZU05KWdKYPCmQoaEcKuxBtMFlNK1r4mdDsN76rjwx6IEtSdNYgWq5Q3MKvaUW7GufFW2tDFdMR6TuFifj64jmPgjUq+keu2TMvi0d00sVwQhEVavqWnz7ptnkMoZYcatDHychGFI2NlJUSPlTQFaASAAAA); }
      @font-face { font-family: Xiaolai; src: url(data:font/woff2;base64,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); }
      @font-face { font-family: Xiaolai; src: url(data:font/woff2;base64,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); }
      @font-face { font-family: Xiaolai; src: url(data:font/woff2;base64,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); }
      @font-face { font-family: Xiaolai; src: url(data:font/woff2;base64,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); }
      @font-face { font-family: Xiaolai; src: url(data:font/woff2;base64,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); }
      @font-face { font-family: Xiaolai; src: url(data:font/woff2;base64,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); }
      @font-face { font-family: Excalifont; src: url(data:font/woff2;base64,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); }</style></defs><rect x="0" y="0" width="820" height="828.3876720246953" fill="#ffffff"></rect><g transform="translate(77.8277772252411 10) rotate(0 293.50318894917126 23.261151627701025)"><text x="293.50318894917126" y="32.788919334407375" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="37.21784260432165px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">HintManager对SQL路由影响对比图</text></g><g stroke-linecap="round" transform="translate(213.88077873842394 275.50175662989426) rotate(0 185.35806963387483 40)"><path d="M20 0 C94.98 -1.81, 163.34 -0.99, 350.72 0 C366.81 -2.71, 371.14 10.22, 370.72 20 C368.78 33.25, 367.39 48.51, 370.72 60 C368.45 76.64, 366.27 77.97, 350.72 80 C240.12 78.55, 130.53 77.73, 20 80 C3.68 77.22, 0.36 75.72, 0 60 C-1.96 47.86, 1.35 43.75, 0 20 C2.2 5.91, 5.94 -0.48, 20 0" stroke="none" stroke-width="0" fill="#e5dbff"></path><path d="M20 0 C86.73 0.2, 152.07 0.34, 350.72 0 M20 0 C110.09 0.36, 199.75 1.19, 350.72 0 M350.72 0 C362.75 0.53, 371.6 8.29, 370.72 20 M350.72 0 C364.25 0.56, 369.38 8.4, 370.72 20 M370.72 20 C372.07 26.95, 371.63 38.63, 370.72 60 M370.72 20 C369.95 35.26, 370.7 50.57, 370.72 60 M370.72 60 C371.31 71.73, 364.53 78.28, 350.72 80 M370.72 60 C369.03 73.4, 364.99 80.81, 350.72 80 M350.72 80 C280.11 82.09, 208.77 82.1, 20 80 M350.72 80 C279.2 82.59, 207.8 82.15, 20 80 M20 80 C4.78 79.71, -0.18 73.86, 0 60 M20 80 C8.5 79.98, 2.16 73.43, 0 60 M0 60 C-1.01 48.25, 0.97 35.53, 0 20 M0 60 C-0.64 49.58, 0.58 37.21, 0 20 M0 20 C0.44 8.66, 5.12 -1.31, 20 0 M0 20 C-0.62 4.6, 7.68 2.27, 20 0" stroke="#7048e8" stroke-width="2" fill="none"></path></g><g transform="translate(222.60719131892773 290.3773987119414) rotate(0 176.66384887695315 20)"><text x="176.66384887695312" y="14.096" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">📝 相同的SQL语句</text><text x="176.66384887695312" y="34.096000000000004" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">INSERT INTO t_order 「...」 VALUES 「...」</text></g><g stroke-linecap="round" transform="translate(10 406.52662821348486) rotate(0 175 205.41808611380998)"><path d="M32 0 C97.38 -1.69, 155.43 -0.78, 318 0 C342.09 -2.71, 350.43 14.22, 350 32 C347.02 145, 346.11 259.32, 350 378.84 C347.73 403.48, 341.56 408.81, 318 410.84 C222.44 409.51, 128 408.6, 32 410.84 C7.68 408.06, 0.36 402.56, 0 378.84 C-0.02 292.89, 2.16 212.21, 0 32 C2.2 9.91, 9.94 -0.48, 32 0" stroke="none" stroke-width="0" fill="#fed7aa"></path><path d="M32 0 C89.85 0.29, 146.16 0.44, 318 0 M32 0 C109.94 0.26, 187.41 1.19, 318 0 M318 0 C338.04 0.53, 350.88 12.29, 350 32 M318 0 C339.53 0.56, 348.67 12.4, 350 32 M350 32 C349.96 106.29, 349.67 183.68, 350 378.84 M350 32 C347.85 166.89, 348.34 301.81, 350 378.84 M350 378.84 C350.6 398.57, 339.82 409.12, 318 410.84 M350 378.84 C348.31 400.23, 340.27 411.65, 318 410.84 M318 410.84 C257.11 412.82, 195.41 412.83, 32 410.84 M318 410.84 C256.12 413.4, 194.37 412.91, 32 410.84 M32 410.84 C8.78 410.55, -0.18 400.7, 0 378.84 M32 410.84 C12.5 410.82, 2.16 400.27, 0 378.84 M0 378.84 C-2.12 279.18, -0.82 178.88, 0 32 M0 378.84 C-0.6 283.38, 0.2 186.65, 0 32 M0 32 C0.44 12.66, 9.12 -1.31, 32 0 M0 32 C-0.62 8.6, 11.68 2.27, 32 0" stroke="#c2410c" stroke-width="2" fill="none"></path></g><g transform="translate(30 426.52662821348486) rotate(0 114.02140808105469 11.25)"><text x="114.02140808105469" y="15.858" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="18px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">🔄 默认分表逻辑「无Hint」</text></g><g stroke-linecap="round" transform="translate(460 406.52662821348486) rotate(0 175 205.93052190560525)"><path d="M32 0 C97.38 -1.69, 155.43 -0.78, 318 0 C342.09 -2.71, 350.43 14.22, 350 32 C347.02 145.33, 346.11 259.98, 350 379.86 C347.73 404.5, 341.56 409.83, 318 411.86 C222.44 410.54, 128 409.63, 32 411.86 C7.68 409.08, 0.36 403.58, 0 379.86 C-0.01 293.67, 2.15 212.73, 0 32 C2.2 9.91, 9.94 -0.48, 32 0" stroke="none" stroke-width="0" fill="#c3fae8"></path><path d="M32 0 C89.85 0.29, 146.16 0.44, 318 0 M32 0 C109.94 0.26, 187.41 1.19, 318 0 M318 0 C338.04 0.53, 350.88 12.29, 350 32 M318 0 C339.53 0.56, 348.67 12.4, 350 32 M350 32 C349.95 106.52, 349.67 184.13, 350 379.86 M350 32 C347.85 167.29, 348.34 302.61, 350 379.86 M350 379.86 C350.6 399.6, 339.82 410.14, 318 411.86 M350 379.86 C348.31 401.26, 340.27 412.67, 318 411.86 M318 411.86 C257.11 413.84, 195.41 413.86, 32 411.86 M318 411.86 C256.12 414.42, 194.37 413.93, 32 411.86 M32 411.86 C8.78 411.57, -0.18 401.72, 0 379.86 M32 411.86 C12.5 411.84, 2.16 401.29, 0 379.86 M0 379.86 C-2.12 279.91, -0.83 179.32, 0 32 M0 379.86 C-0.6 284.12, 0.2 187.11, 0 32 M0 32 C0.44 12.66, 9.12 -1.31, 32 0 M0 32 C-0.62 8.6, 11.68 2.27, 32 0" stroke="#2f9e44" stroke-width="2" fill="none"></path></g><g transform="translate(480 426.52662821348486) rotate(0 113.08539581298828 11.25)"><text x="113.08539581298828" y="15.858" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="18px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">🎯 Hint强制路由「有Hint」</text></g><g stroke-linecap="round" transform="translate(30 476.52662821348486) rotate(0 155 30)"><path d="M15 0 C79.09 -1.67, 135.75 -0.74, 295 0 C307.76 -2.71, 310.43 8.56, 310 15 C308.15 25, 306.76 37, 310 45 C307.73 58.31, 307.22 57.97, 295 60 C201.46 58.7, 109.05 57.78, 15 60 C2.02 57.22, 0.36 57.39, 0 45 C-2.03 35.29, 1.29 33.62, 0 15 C2.2 4.24, 4.27 -0.48, 15 0" stroke="none" stroke-width="0" fill="#fff7ed"></path><path d="M15 0 C71.66 0.3, 126.76 0.46, 295 0 M15 0 C91.31 0.25, 167.14 1.18, 295 0 M295 0 C303.7 0.53, 310.88 6.63, 310 15 M295 0 C305.2 0.56, 308.67 6.73, 310 15 M310 15 C311.4 19.78, 310.96 29.28, 310 45 M310 15 C309.32 26.37, 310.07 37.78, 310 45 M310 45 C310.6 53.4, 305.48 58.28, 295 60 M310 45 C308.31 55.06, 305.94 60.81, 295 60 M295 60 C235.42 61.96, 175.01 61.98, 15 60 M295 60 C234.42 62.55, 173.96 62.06, 15 60 M15 60 C3.11 59.71, -0.18 55.53, 0 45 M15 60 C6.83 59.98, 2.16 55.1, 0 45 M0 45 C-0.94 36.11, 1.04 26.26, 0 15 M0 45 C-0.63 37.35, 0.59 27.74, 0 15 M0 15 C0.44 7, 3.45 -1.31, 15 0 M0 15 C-0.62 2.93, 6.01 2.27, 15 0" stroke="#c2410c" stroke-width="1" fill="none"></path></g><g transform="translate(50 491.52662821348486) rotate(0 105.6929702758789 17.5)"><text x="105.6929702758789" y="12.334" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">1️⃣ SQL解析</text><text x="105.6929702758789" y="29.834" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">解析INSERT语句，提取分片键值</text></g><g stroke-linecap="round" transform="translate(30 556.5266282134849) rotate(0 155 30)"><path d="M15 0 C79.09 -1.67, 135.75 -0.74, 295 0 C307.76 -2.71, 310.43 8.56, 310 15 C308.15 25, 306.76 37, 310 45 C307.73 58.31, 307.22 57.97, 295 60 C201.46 58.7, 109.05 57.78, 15 60 C2.02 57.22, 0.36 57.39, 0 45 C-2.03 35.29, 1.29 33.62, 0 15 C2.2 4.24, 4.27 -0.48, 15 0" stroke="none" stroke-width="0" fill="#fff7ed"></path><path d="M15 0 C71.66 0.3, 126.76 0.46, 295 0 M15 0 C91.31 0.25, 167.14 1.18, 295 0 M295 0 C303.7 0.53, 310.88 6.63, 310 15 M295 0 C305.2 0.56, 308.67 6.73, 310 15 M310 15 C311.4 19.78, 310.96 29.28, 310 45 M310 15 C309.32 26.37, 310.07 37.78, 310 45 M310 45 C310.6 53.4, 305.48 58.28, 295 60 M310 45 C308.31 55.06, 305.94 60.81, 295 60 M295 60 C235.42 61.96, 175.01 61.98, 15 60 M295 60 C234.42 62.55, 173.96 62.06, 15 60 M15 60 C3.11 59.71, -0.18 55.53, 0 45 M15 60 C6.83 59.98, 2.16 55.1, 0 45 M0 45 C-0.94 36.11, 1.04 26.26, 0 15 M0 45 C-0.63 37.35, 0.59 27.74, 0 15 M0 15 C0.44 7, 3.45 -1.31, 15 0 M0 15 C-0.62 2.93, 6.01 2.27, 15 0" stroke="#c2410c" stroke-width="1" fill="none"></path></g><g transform="translate(50 571.5266282134849) rotate(0 63.699920654296875 17.5)"><text x="63.699920654296875" y="12.334" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">2️⃣ 分片算法计算</text><text x="63.699920654296875" y="29.834" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">user_id % 128 = 2</text></g><g stroke-linecap="round" transform="translate(30 636.5266282134849) rotate(0 155 30)"><path d="M15 0 C79.09 -1.67, 135.75 -0.74, 295 0 C307.76 -2.71, 310.43 8.56, 310 15 C308.15 25, 306.76 37, 310 45 C307.73 58.31, 307.22 57.97, 295 60 C201.46 58.7, 109.05 57.78, 15 60 C2.02 57.22, 0.36 57.39, 0 45 C-2.03 35.29, 1.29 33.62, 0 15 C2.2 4.24, 4.27 -0.48, 15 0" stroke="none" stroke-width="0" fill="#fff7ed"></path><path d="M15 0 C71.66 0.3, 126.76 0.46, 295 0 M15 0 C91.31 0.25, 167.14 1.18, 295 0 M295 0 C303.7 0.53, 310.88 6.63, 310 15 M295 0 C305.2 0.56, 308.67 6.73, 310 15 M310 15 C311.4 19.78, 310.96 29.28, 310 45 M310 15 C309.32 26.37, 310.07 37.78, 310 45 M310 45 C310.6 53.4, 305.48 58.28, 295 60 M310 45 C308.31 55.06, 305.94 60.81, 295 60 M295 60 C235.42 61.96, 175.01 61.98, 15 60 M295 60 C234.42 62.55, 173.96 62.06, 15 60 M15 60 C3.11 59.71, -0.18 55.53, 0 45 M15 60 C6.83 59.98, 2.16 55.1, 0 45 M0 45 C-0.94 36.11, 1.04 26.26, 0 15 M0 45 C-0.63 37.35, 0.59 27.74, 0 15 M0 15 C0.44 7, 3.45 -1.31, 15 0 M0 15 C-0.62 2.93, 6.01 2.27, 15 0" stroke="#c2410c" stroke-width="1" fill="none"></path></g><g transform="translate(50 651.5266282134849) rotate(0 77 17.5)"><text x="77" y="12.334" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">3️⃣ 路由决策</text><text x="77" y="29.834" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">根据算法结果选择目标表</text></g><g stroke-linecap="round" transform="translate(30 716.5266282134849) rotate(0 155 40)"><path d="M20 0 C81.95 -1.63, 136.3 -0.69, 290 0 C306.09 -2.71, 310.43 10.22, 310 20 C308.07 33.25, 306.67 48.51, 310 60 C307.73 76.64, 305.56 77.97, 290 80 C199.82 78.73, 110.8 77.79, 20 80 C3.68 77.22, 0.36 75.72, 0 60 C-1.96 47.86, 1.35 43.75, 0 20 C2.2 5.91, 5.94 -0.48, 20 0" stroke="none" stroke-width="0" fill="#fecaca"></path><path d="M20 0 C74.67 0.33, 127.75 0.48, 290 0 M20 0 C93.59 0.22, 166.7 1.18, 290 0 M290 0 C302.04 0.53, 310.88 8.29, 310 20 M290 0 C303.53 0.56, 308.67 8.4, 310 20 M310 20 C311.35 26.95, 310.91 38.63, 310 60 M310 20 C309.24 35.26, 309.99 50.57, 310 60 M310 60 C310.6 71.73, 303.82 78.28, 290 80 M310 60 C308.31 73.4, 304.27 80.81, 290 80 M290 80 C232.59 81.93, 174.34 81.94, 20 80 M290 80 C231.57 82.54, 173.27 82.03, 20 80 M20 80 C4.78 79.71, -0.18 73.86, 0 60 M20 80 C8.5 79.98, 2.16 73.43, 0 60 M0 60 C-1.01 48.25, 0.97 35.53, 0 20 M0 60 C-0.64 49.58, 0.58 37.21, 0 20 M0 20 C0.44 8.66, 5.12 -1.31, 20 0 M0 20 C-0.62 4.6, 7.68 2.27, 20 0" stroke="#dc2626" stroke-width="2" fill="none"></path></g><g transform="translate(50 736.5266282134849) rotate(0 128.64788818359375 20)"><text x="128.64788818359375" y="14.096" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">📊 最终执行</text><text x="128.64788818359375" y="34.096000000000004" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">INSERT INTO t_order_2 「...」</text></g><g stroke-linecap="round" transform="translate(480 476.52662821348486) rotate(0 155 30)"><path d="M15 0 C79.09 -1.67, 135.75 -0.74, 295 0 C307.76 -2.71, 310.43 8.56, 310 15 C308.15 25, 306.76 37, 310 45 C307.73 58.31, 307.22 57.97, 295 60 C201.46 58.7, 109.05 57.78, 15 60 C2.02 57.22, 0.36 57.39, 0 45 C-2.03 35.29, 1.29 33.62, 0 15 C2.2 4.24, 4.27 -0.48, 15 0" stroke="none" stroke-width="0" fill="#f0fdf4"></path><path d="M15 0 C71.66 0.3, 126.76 0.46, 295 0 M15 0 C91.31 0.25, 167.14 1.18, 295 0 M295 0 C303.7 0.53, 310.88 6.63, 310 15 M295 0 C305.2 0.56, 308.67 6.73, 310 15 M310 15 C311.4 19.78, 310.96 29.28, 310 45 M310 15 C309.32 26.37, 310.07 37.78, 310 45 M310 45 C310.6 53.4, 305.48 58.28, 295 60 M310 45 C308.31 55.06, 305.94 60.81, 295 60 M295 60 C235.42 61.96, 175.01 61.98, 15 60 M295 60 C234.42 62.55, 173.96 62.06, 15 60 M15 60 C3.11 59.71, -0.18 55.53, 0 45 M15 60 C6.83 59.98, 2.16 55.1, 0 45 M0 45 C-0.94 36.11, 1.04 26.26, 0 15 M0 45 C-0.63 37.35, 0.59 27.74, 0 15 M0 15 C0.44 7, 3.45 -1.31, 15 0 M0 15 C-0.62 2.93, 6.01 2.27, 15 0" stroke="#2f9e44" stroke-width="1" fill="none"></path></g><g transform="translate(500 491.52662821348486) rotate(0 122.3739013671875 17.5)"><text x="122.3739013671875" y="12.334" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">1️⃣ HintManager设置</text><text x="122.3739013671875" y="29.834" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">setDatabaseShardingValue「『5』」</text></g><g stroke-linecap="round" transform="translate(480 556.5266282134849) rotate(0 155 30)"><path d="M15 0 C79.09 -1.67, 135.75 -0.74, 295 0 C307.76 -2.71, 310.43 8.56, 310 15 C308.15 25, 306.76 37, 310 45 C307.73 58.31, 307.22 57.97, 295 60 C201.46 58.7, 109.05 57.78, 15 60 C2.02 57.22, 0.36 57.39, 0 45 C-2.03 35.29, 1.29 33.62, 0 15 C2.2 4.24, 4.27 -0.48, 15 0" stroke="none" stroke-width="0" fill="#f0fdf4"></path><path d="M15 0 C71.66 0.3, 126.76 0.46, 295 0 M15 0 C91.31 0.25, 167.14 1.18, 295 0 M295 0 C303.7 0.53, 310.88 6.63, 310 15 M295 0 C305.2 0.56, 308.67 6.73, 310 15 M310 15 C311.4 19.78, 310.96 29.28, 310 45 M310 15 C309.32 26.37, 310.07 37.78, 310 45 M310 45 C310.6 53.4, 305.48 58.28, 295 60 M310 45 C308.31 55.06, 305.94 60.81, 295 60 M295 60 C235.42 61.96, 175.01 61.98, 15 60 M295 60 C234.42 62.55, 173.96 62.06, 15 60 M15 60 C3.11 59.71, -0.18 55.53, 0 45 M15 60 C6.83 59.98, 2.16 55.1, 0 45 M0 45 C-0.94 36.11, 1.04 26.26, 0 15 M0 45 C-0.63 37.35, 0.59 27.74, 0 15 M0 15 C0.44 7, 3.45 -1.31, 15 0 M0 15 C-0.62 2.93, 6.01 2.27, 15 0" stroke="#2f9e44" stroke-width="1" fill="none"></path></g><g transform="translate(500 571.5266282134849) rotate(0 99.36491394042969 17.5)"><text x="99.36491394042969" y="12.334" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">2️⃣ Hint检测</text><text x="99.36491394042969" y="29.834" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">ShardingSphere检测到Hint存在</text></g><g stroke-linecap="round" transform="translate(480 636.5266282134849) rotate(0 155 30)"><path d="M15 0 C79.09 -1.67, 135.75 -0.74, 295 0 C307.76 -2.71, 310.43 8.56, 310 15 C308.15 25, 306.76 37, 310 45 C307.73 58.31, 307.22 57.97, 295 60 C201.46 58.7, 109.05 57.78, 15 60 C2.02 57.22, 0.36 57.39, 0 45 C-2.03 35.29, 1.29 33.62, 0 15 C2.2 4.24, 4.27 -0.48, 15 0" stroke="none" stroke-width="0" fill="#f0fdf4"></path><path d="M15 0 C71.66 0.3, 126.76 0.46, 295 0 M15 0 C91.31 0.25, 167.14 1.18, 295 0 M295 0 C303.7 0.53, 310.88 6.63, 310 15 M295 0 C305.2 0.56, 308.67 6.73, 310 15 M310 15 C311.4 19.78, 310.96 29.28, 310 45 M310 15 C309.32 26.37, 310.07 37.78, 310 45 M310 45 C310.6 53.4, 305.48 58.28, 295 60 M310 45 C308.31 55.06, 305.94 60.81, 295 60 M295 60 C235.42 61.96, 175.01 61.98, 15 60 M295 60 C234.42 62.55, 173.96 62.06, 15 60 M15 60 C3.11 59.71, -0.18 55.53, 0 45 M15 60 C6.83 59.98, 2.16 55.1, 0 45 M0 45 C-0.94 36.11, 1.04 26.26, 0 15 M0 45 C-0.63 37.35, 0.59 27.74, 0 15 M0 15 C0.44 7, 3.45 -1.31, 15 0 M0 15 C-0.62 2.93, 6.01 2.27, 15 0" stroke="#2f9e44" stroke-width="1" fill="none"></path></g><g transform="translate(500 651.5266282134849) rotate(0 69.27198028564453 17.5)"><text x="69.27198028564453" y="12.334" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">3️⃣ 跳过分片算法</text><text x="69.27198028564453" y="29.834" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">直接使用Hint指定的值</text></g><g stroke-linecap="round" transform="translate(480 716.5266282134849) rotate(0 155 40)"><path d="M20 0 C81.95 -1.63, 136.3 -0.69, 290 0 C306.09 -2.71, 310.43 10.22, 310 20 C308.07 33.25, 306.67 48.51, 310 60 C307.73 76.64, 305.56 77.97, 290 80 C199.82 78.73, 110.8 77.79, 20 80 C3.68 77.22, 0.36 75.72, 0 60 C-1.96 47.86, 1.35 43.75, 0 20 C2.2 5.91, 5.94 -0.48, 20 0" stroke="none" stroke-width="0" fill="#bbf7d0"></path><path d="M20 0 C74.67 0.33, 127.75 0.48, 290 0 M20 0 C93.59 0.22, 166.7 1.18, 290 0 M290 0 C302.04 0.53, 310.88 8.29, 310 20 M290 0 C303.53 0.56, 308.67 8.4, 310 20 M310 20 C311.35 26.95, 310.91 38.63, 310 60 M310 20 C309.24 35.26, 309.99 50.57, 310 60 M310 60 C310.6 71.73, 303.82 78.28, 290 80 M310 60 C308.31 73.4, 304.27 80.81, 290 80 M290 80 C232.59 81.93, 174.34 81.94, 20 80 M290 80 C231.57 82.54, 173.27 82.03, 20 80 M20 80 C4.78 79.71, -0.18 73.86, 0 60 M20 80 C8.5 79.98, 2.16 73.43, 0 60 M0 60 C-1.01 48.25, 0.97 35.53, 0 20 M0 60 C-0.64 49.58, 0.58 37.21, 0 20 M0 20 C0.44 8.66, 5.12 -1.31, 20 0 M0 20 C-0.62 4.6, 7.68 2.27, 20 0" stroke="#16a34a" stroke-width="2" fill="none"></path></g><g transform="translate(500 736.5266282134849) rotate(0 127.99188995361328 20)"><text x="127.99188995361328" y="14.096" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">🎯 最终执行</text><text x="127.99188995361328" y="34.096000000000004" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">INSERT INTO t_order_5 「...」</text></g><g stroke-linecap="round"><g transform="translate(360 356.52662821348486) rotate(0 -87.5 25)"><path d="M0.24 0.51 C-28.63 8.98, -145.33 41.62, -174.42 49.81 M-1.09 -0.26 C-29.96 8.48, -146.13 42.73, -175.07 51.24" stroke="#c2410c" stroke-width="3" fill="none"></path></g><g transform="translate(360 356.52662821348486) rotate(0 -87.5 25)"><path d="M-154.95 36.4 C-158.24 40.38, -163.44 43.54, -175.07 51.24 M-154.95 36.4 C-160.35 39.57, -166.25 44.83, -175.07 51.24" stroke="#c2410c" stroke-width="3" fill="none"></path></g><g transform="translate(360 356.52662821348486) rotate(0 -87.5 25)"><path d="M-150.11 52.81 C-154.24 53.51, -160.4 53.4, -175.07 51.24 M-150.11 52.81 C-156.69 51.5, -163.9 52.29, -175.07 51.24" stroke="#c2410c" stroke-width="3" fill="none"></path></g></g><mask></mask><g stroke-linecap="round"><g transform="translate(460 356.52662821348486) rotate(0 87.5 25)"><path d="M0.24 0.51 C29.71 8.98, 146.34 41.62, 175.58 49.81 M-1.09 -0.26 C28.37 8.48, 145.54 42.73, 174.93 51.24" stroke="#2f9e44" stroke-width="3" fill="none"></path></g><g transform="translate(460 356.52662821348486) rotate(0 87.5 25)"><path d="M149.99 52.89 C155.85 53.47, 159.66 53.34, 174.93 51.24 M149.99 52.89 C156.98 51.75, 163.34 52.52, 174.93 51.24" stroke="#2f9e44" stroke-width="3" fill="none"></path></g><g transform="translate(460 356.52662821348486) rotate(0 87.5 25)"><path d="M154.77 36.47 C159.54 40.35, 162.39 43.5, 174.93 51.24 M154.77 36.47 C160.32 39.78, 165.38 45.02, 174.93 51.24" stroke="#2f9e44" stroke-width="3" fill="none"></path></g></g><mask></mask><g stroke-linecap="round" transform="translate(135.40308199382616 113.30603075434647) rotate(0 251.02487158359054 74.86063796206341)"><path d="M32 0 C127.43 -4.12, 221.88 -4.1, 470.05 0 C488.35 -1.33, 505.36 9.94, 502.05 32 C498.29 63.71, 501.37 97.4, 502.05 117.72 C504.92 139.03, 494.76 149.87, 470.05 149.72 C343.76 149.22, 219.92 148.09, 32 149.72 C9.82 150.05, 1.07 136.88, 0 117.72 C-0.16 90.13, -3.74 53, 0 32 C-0.97 7.42, 12.25 3.56, 32 0" stroke="none" stroke-width="0" fill="#f3f4f6"></path><path d="M32 0 C120.04 0.04, 207.06 0.14, 470.05 0 M470.05 0 C490.82 -1.73, 501.26 11.06, 502.05 32 M502.05 32 C500.56 51.5, 502.73 71.58, 502.05 117.72 M502.05 117.72 C502.22 139.54, 490.22 151.23, 470.05 149.72 M470.05 149.72 C375.55 149.34, 280.05 150.84, 32 149.72 M32 149.72 C12.46 148.08, 1.39 138.19, 0 117.72 M0 117.72 C0.33 93.12, 0.21 69.99, 0 32 M0 32 C-1.47 10.72, 11.48 0.7, 32 0" stroke="#7048e8" stroke-width="3.5" fill="none" stroke-dasharray="8 11"></path></g><g transform="translate(170.7761557476848 121.0075717512596) rotate(0 214.35718825920077 66.66166529333873)"><text x="214.35718825920074" y="15.661047232915049" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="17.776444078223665px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">🔑 核心机制说明:</text><text x="214.35718825920074" y="37.88160233069463" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="17.776444078223665px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic"></text><text x="214.35718825920074" y="60.102157428474214" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="17.776444078223665px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• HintManager通过ThreadLocal存储路由提示</text><text x="214.35718825920074" y="82.3227125262538" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="17.776444078223665px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• ShardingSphere优先检查Hint，存在时跳过分片算法</text><text x="214.35718825920074" y="104.54326762403338" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="17.776444078223665px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• 相同SQL + 不同Hint = 不同目标表</text><text x="214.35718825920074" y="126.76382272181296" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="17.776444078223665px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• 实现了对分表逻辑的完全控制和规避</text></g><g transform="translate(125.31305727392362 77.05812661673485) rotate(0 260.8623046875 8.75)"><text x="0" y="12.334" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="14px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">🎯 关键点: 相同SQL语句，通过Hint实现不同表的写入，完全规避了默认分片逻辑！</text></g><g transform="translate(380 646.5266282134849) rotate(0 14.5679931640625 15)"><text x="14.5679931640625" y="21.144" font-family="Excalifont, Xiaolai, sans-serif, Segoe UI Emoji" font-size="24px" fill="#7048e8" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">VS</text></g></svg>