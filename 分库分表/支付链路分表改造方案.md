# 一、背景与核心诉求
## 1.1. 项目背景












1. id与自增的问题
2. 逻辑与切换



系统当前已基于业务标识 `counterGuaranteeNo` 实现了数据库层面的水平分片（分库）。然而，随着业务量的持续高速增长，**单个分库内部的核心表**（如 `t_trans_order`）再次出现巨大的数据量积压，单表数据已达亿级，严重影响了查询性能和数据库维护效率。因此，在现有分库基础上，引入库内分表机制势在必行。

## 1.2. 核心诉求
1. **快速解决性能瓶颈**: 尽快解决当前大表导致的性能问题，保障业务稳定。
2. **最小化上线风险**: 避免"大爆炸式"的架构变更，优先采用对现有系统侵入性小、风险可控的方案。
3. **构建长期健康架构**: 最终方案应具备良好的扩展性、稳定性和可维护性，能够支撑未来业务发展。
4. **保障数据平滑迁移**: 确保历史数据从单表到多表迁移的过程安全、可靠，对在线业务无感知。

# 二、现状分析
## 业务流程
上游还款调用方

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/*************-1ca409d4-f6a2-4579-b16d-507bd2ec49e0.svg)

支付系统业务处理

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/*************-2582640f-3b7c-4a7d-95d8-d48095bdb493.svg)

## 已实现的分库机制
当前系统采用应用层分片，核心是 Spring 的 `DynamicDataSource` (继承自 `AbstractRoutingDataSource`)。其工作流程为：

1. 业务代码在 `ServiceContext` (基于 `ThreadLocal`) 中设置分库键 `counterGuaranteeNo`。
2. DAO 层执行时，`DynamicDataSource` 通过 `determineCurrentLookupKey()` 方法获取该分库键，路由到正确的物理数据库。  
**此机制在第一阶段改造中将保留。**

### 动态数据源初始化过程
![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/*************-700a55e7-744e-407e-bc62-6cea99308dc6.svg)

1. **Bean实例化 (Spring驱动)**
    - Spring容器根据XML配置创建`DynamicDataSource`实例，并注入`dal-config.xml`的路径。这是加载本地路由规则的入口。
2. **启动初始化 (Spring回调)**
    - Spring调用`DynamicDataSource`的`afterPropertiesSet()`方法。
    - **关键点：** `DynamicDataSource`将所有复杂逻辑委托给一个新建的`DynamicDataSourceHelper`实例。
3. **加载本地路由规则 (**`loadAndParseDalConfig`**)**
    - `Helper`解析`dal-config.xml`文件，此步骤只加载**方法级的路由规则**（如读/写操作定义），存入`DalConfig`对象。此时，`DalConfig`中数据源连接信息为空。
4. **获取远程数据源配置 (**`getDataSource`**)**
    - `Helper`通过RPC调用远程`APS`服务，获取所有数据库连接信息。
    - **关键点：**
        * **远程驱动：** 连接信息由配置中心集中管理。
        * **提取分库依据：** 通过反射提取所有`counterGuaranteeNo`（融担编号）。
        * **填充**`DalConfig`**:** 将远程配置填充回`DalConfig`对象，使其成为包含"路由规则"和"数据源详情"的完全体。
5. **动态注册Bean定义 (**`registerDataSourceBean`**)**
    - `Helper`遍历`DalConfig`中的每个数据源配置。
    - **关键点：**
        * **编程方式定义Bean：** 以编程方式创建`BeanDefinition`（Java版的`<bean>`标签），设置好`DruidDataSource`的类及所有属性。
        * **注册到Spring核心：** 将`BeanDefinition`动态注册到Spring的`DefaultListableBeanFactory`中。此时Bean尚未创建，但Spring已知道"如何"创建它。
6. **装配最终路由表 (**`initDynamicDataSource`**)**
    - 这是初始化的最后一步，负责将所有组件"组装"起来。
    - `Helper`为每个数据源生成一个**路由Key**（如`666_MASTER`）。
    - 调用`applicationContext.getBean()`，触发Spring根据上一步注册的`BeanDefinition`来**创建并初始化**真正的`DruidDataSource`连接池实例。
    - **关键点：**
        * **最终路由表：** 将（路由Key, 数据源实例）的键值对存入一个`Map`。
        * **设置给**`DynamicDataSource`**:** 调用`setTargetDataSources()`方法，将此Map设置为`DynamicDataSource`的核心路由表。
        * **策略注入：** 创建并注入`DefaultDataSourceRouter`（路由决策器）和负载均衡策略实例。





### 数据库路由与SQL执行
![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1750667464843-d64e5a25-51c0-49b2-976a-90dff7ffb736.svg)

#### **第1步：AOP拦截规则的定义**
框架的路由能力始于AOP（面向切面编程）。Spring容器通过解析XML配置文件，得知需要对哪些方法进行拦截。

+ **配置文件**: `css-trans-core/src/main/resources/beans/beans-datasource.xml`
+ **切面(Advice)定义**: 文件中定义了一个名为 `dynamicDataSourceParamsAdvice` 的Bean，其实例是 `com.fb.framework.dal.interceptor.DynamicDataSourceParamsAdvice` 类。
+ **切点(Pointcut)定义**: 文件中定义了一个切点 `dynamicDataSourcePoint`，其`expression`表达式明确指示Spring去拦截 `com.fb.css.trans.dao` 包及其所有子包下的全部公共方法。
+ **织入(Advisor)定义**: 最后，`aop:advisor`标签将上述"切面"和"切点"绑定。这意味着，**任何对DAO层方法的调用，都会被 **`DynamicDataSourceParamsAdvice`** 拦截**。

#### **第2步：读操作路由 (**`getBizOderDTOBySeqNo`**)**
**场景**: 业务代码调用 `bizOrderService.getBizOderDTOBySeqNo(seqNo)`，该方法内部实际调用 `ICssBizOrderDao.getBizOderBySeqNo(seqNo)`。

1. **调用被拦截**: Spring AOP代理侦测到对DAO方法的调用，`DynamicDataSourceParamsAdvice` 的 `invoke` 方法被执行。
2. **采集与压栈**: `invoke` 方法调用 `recordDataSourceParams`：
    - **读**`dal-config.xml`: `DalConfigUtil.getMethodRouteConfig` 查到 `getBizOderBySeqNo` 方法的路由配置为 `use="Slave"`。
    - **读**`ServiceContext`: `ServiceContext.getContext().getFbAccessNo()` 从线程上下文中获取到融担编号，例如 `"666"`。
    - **存入**`ContextHolder`: 包含 `{use: "Slave", counterGuaranteeNo: "666"}` 的 `MethodInfo` 对象被压入 `ContextHolder` 的线程栈。
3. **获取数据库连接**: `invoke` 调用 `invocation.proceed()`，MyBatis开始执行，并向 `DynamicDataSource` 请求连接。
4. **构建路由Key**: `DynamicDataSource.determineCurrentLookupKey()` 被调用：
    - 它从 `ServiceContext` 拿到`"666"`。
    - 它调用 `DefaultDataSourceRouter.selectCurrentDataSourceKey()`。Router内部的 `isSlave()` 方法从 `ContextHolder` 读到 `use="Slave"`，判定为真。
    - `Router` 调用负载均衡器（如 `RandomSlaveLoadBalance`）返回一个从库后缀，例如 `"slave_0"`。
    - 最终返回的路由Key为 `"666_slave_0"`。
5. **执行SQL**: `DynamicDataSource` 从其内部的 `targetDataSources` Map中，根据Key `"666_slave_0"` 找到对应的**从库**数据源实例，并获取连接，最终在从库上执行`SELECT`语句。
6. **上下文清理**: DAO方法返回后，`invoke`方法的`finally`块执行`ContextHolder.popMethodInfo()`，清理线程上下文。

#### **第3步：写操作路由 (**`createBizOrder`**)**
**场景**: 业务代码接着调用 `bizOrderService.createBizOrder(dto)`，该方法内部实际调用 `ICssBizOrderDao.insert(dto)`。

1. **调用被拦截**: 对 `ICssBizOrderDao.insert()` 的调用再次被 `DynamicDataSourceParamsAdvice` 拦截。
2. **采集与压栈**: `recordDataSourceParams` 再次执行：
    - **读**`dal-config.xml`: `DalConfigUtil.getMethodRouteConfig` 查到 `insert` 方法的路由配置为 `use="Master"`。
    - **读**`ServiceContext`: 再次获取到融担编号`"666"`。
    - **存入**`ContextHolder`: 包含 `{use: "Master", counterGuaranteeNo: "666"}` 的**新**`MethodInfo`对象被压入 `ContextHolder` 的线程栈顶。
3. **获取数据库连接**: MyBatis向 `DynamicDataSource` 请求连接。
4. **构建路由Key**: `DynamicDataSource.determineCurrentLookupKey()` 被调用：
    - 它从 `ServiceContext` 拿到`"666"`。
    - 它调用 `DefaultDataSourceRouter.selectCurrentDataSourceKey()`。Router内部的 `isMaster()` 方法从 `ContextHolder` 读到 `use="Master"`，判定为真。
    - `Router` 立即返回 `"master"`。
    - 最终返回的路由Key为 `"666_master"`。
5. **执行SQL**: `DynamicDataSource` 从其 `targetDataSources` Map中，根据Key `"666_master"` 找到对应的**主库**数据源实例，并获取连接，最终在主库上执行`INSERT`语句。
6. **上下文清理**: DAO方法返回后，`invoke`方法的`finally`块再次执行`ContextHolder.popMethodInfo()`，清理刚刚压入的`MethodInfo`。

## 涉及到的表
### 表 1: `biz_order` (业务订单表)
+ **分表依据**: **总量 ~8.9 亿，日增 ~660 万。数据量巨大，必须分表。**

| 业务场景 | 场景分类 | 涉及字段 (写) | `WHERE` 条件中的关键字段 (读) |
| :--- | :--- | :--- | :--- |
| **创建业务订单**   (`QuickPayServiceImpl#doCreateBizOrder`) | 核心流程 | 全量字段 | `biz_seq_no` |
| **按业务流水号查询**   (`BizOrderServiceImpl#getBizOderDTOBySeqNo`) | 核心流程 | N/A | `biz_seq_no` |
| **按主键ID查询**   (`BizOrderServiceImpl#getBizOderDTOById`) | 核心流程 | N/A | `id` |
| **按关联ID查询**   (`BizOrderServiceImpl#getBizOderDTOByRelatedId`) | 核心流程 | N/A | `related_id` |
| **按防重码查询**   (`BizOrderServiceImpl#checkSameBizCheckNo`) | 核心流程 | N/A | `biz_check_no`, `biz_status` |
| **订单冲正**   (`BizOrderServiceImpl#revertBizOrderForTransactional`) | 核心流程 | **Update**: `biz_seq_no`, `biz_check_no`   **Insert**: 全量字段 (冲正单) | `id`, `biz_seq_no` (用于Update) |
| **更新订单状态**   (`ExtBizOrderMapper.xml#updateStatus`) | 核心流程 | `biz_status`, `update_datetime`, `updated_by`, `completion_time` | `id`, `biz_status` |
| **按状态批量拉取**   (`BizOrderServiceImpl#dealBizOrder`) | 后台/定时任务 | N/A | `biz_status`, `id` |
| **按ID范围拉取**   (`BizOrderServiceImpl#dealBizOrderByCompletionTimeRange`) | 后台/定时任务 | N/A | `id` |
| **OMS后台多条件查询**   (`BizOrderServiceImpl#listByPage`) | 后台/管理后台 | N/A | **多条件组合**: `biz_seq_no`, `cust_no`, `project_no`, `biz_type`, `biz_status`, `create_datetime` 范围等 |
| **按流水号模糊查询**   (`BizOrderServiceImpl#selectByLikeSeqNo`) | 后台/管理后台 | N/A | `biz_seq_no` (LIKE) |


---

### 表 2: `biz_order_detail` (业务订单明细表)
+ **分表依据**: **推断分表**。该表是 `biz_order` 的下游一对多关联表，上游 `biz_order` 数据量巨大（8.9亿），因此 `biz_order_detail` 的数据量会更大，必须进行分表。

| 业务场景 | 场景分类 | 涉及字段 (写) | `WHERE` 条件中的关键字段 (读) |
| :--- | :--- | :--- | :--- |
| **创建订单明细**   (`BizOrderDetailServiceImpl#createAllBizOrderDetail`) | 核心流程 | 全量字段 | N/A |
| **创建冲正明细**   (`BizOrderDetailServiceImpl#createAllRevertBizOrderDetail`) | 核心流程 | 全量字段 (冲正单) | N/A |
| **按业务订单ID查询**   (`BizOrderDetailServiceImpl#selectByBizOrderId`) | 核心流程 | N/A | `biz_order_id` |
| **冲正时查询原明细**   (`BizOrderActionImpl#revertBizOrder`) | 核心流程 | N/A | `related_id` (关联原 `biz_order` 的 ID) |
| **更新明细状态**   (`BizOrderDetailActionImpl`) | 核心流程 | `order_status`, `update_datetime`, `updated_by` | `id` |
| **按状态批量拉取**   (`BizOrderDetailServiceImpl#dealBizOrderDetail`) | 后台/定时任务 | N/A | `order_status`, `id` |
| **按主键ID查询**   (`BizOrderDetailServiceImpl#getBizOrderDetailById`) | 核心流程 | N/A | `id` |


---

### 表 3: `trans_order` (交易订单表)
+ **分表依据**: **总量 ~21.8 亿，日增 ~1638 万。数据量和增速都极其巨大，必须分表。**

| 业务场景 | 场景分类 | 涉及字段 (写) | `WHERE` 条件中的关键字段 (读) |
| :--- | :--- | :--- | :--- |
| **主流程创建交易单**   (`QuickPayActionServiceImpl#getAllTransOrder`) | 核心流程 | 全量字段 (批量) | N/A (写入) |
| **创建退款/冲正单**   (`TransOrderServiceImpl`) | 核心流程 | 全量字段 (退款/冲正) | N/A (写入) |
| **按业务订单ID查询**   (`TransOrderServiceImpl#getTransOrderDTOsByBizOrderId`) | 核心流程 | N/A | `biz_order_id` |
| **更新订单状态**   (`ExtTransOrderMapper.xml#updateStatus`) | 核心流程 | `trans_status`, `trans_ext_data`, `updated_by`, `pay_finish_time`, `completion_time` | `id`, `trans_status` (fromState) |
| **更新完成时间**   (`TransOrderServiceImpl#updateCompletionAndPayFinishTime`) | 核心流程 | `completion_time`, `pay_finish_time`, `trans_ext_data` | `id` |
| **按状态批量拉取**   (`TransOrderServiceImpl#dealTransOrder`) | 后台/定时任务 | N/A | `trans_status`, `id` |
| **OMS后台多条件查询**   (`TransOrderDao#listTransOrderForOms`) | 后台/管理后台 | N/A | **多条件组合**: `biz_seq_no`, `cust_no`, `project_no`, `trans_type`, `trans_status`, `create_datetime` 范围等 |
| **按主键ID查询**   (`TransOrderServiceImpl#getTransOrderDTOById`) | 核心流程 | N/A | `id` |
| **按唯一键查询**   (`ExtTransOrderMapper.xml#selectUniqueOrder`) | 核心流程 | N/A | `biz_order_id`, `trans_num` |
| **按明细ID查询**   (`TransOrderServiceImpl#getTransOrderListByBizOrderDetailId`) | 核心流程 | N/A | `biz_order_detail_id` |


---

### 表 4: `pay_route_order` (支付路由订单表)
+ **分表依据**: **推断分表**。该表是 `trans_order` 的下游，两者数据量级相当。上游 `trans_order` 数据量巨大（~21.8亿），因此 `pay_route_order` 的数据量也必然是巨大的，必须进行分表。

| 业务场景 | 场景分类 | 涉及字段 (写) | `WHERE` 条件中的关键字段 (读) |
| :--- | :--- | :--- | :--- |
| **支付路由时获取/创建**   (`PayRouteOrderHelperImpl#getOrCreatePayRouteOrder`) | 核心流程 | 全量字段 (创建时) | `front_order_no` (唯一键查询) |
| **异步更新路由结果**   (`PayRouteOrderEventListener`) | 核心流程 | `status`, `resp_info`, `update_time` 等 | `id` |
| **退款时查询原路由**   (`PayRouteOrderActionServiceImpl`) | 核心流程 | N/A | `front_req_no` |
| **按状态批量拉取**   (`PayRouteOrderInnerServiceImpl#dealPayRouteOrder`) | 后台/定时任务 | N/A | `status`, `id` |
| **按主键ID查询**   (`PayRouteOrderDao#selectByPrimaryKey`) | 核心流程 | N/A | `id` |


---

### 表 5: `t_withhold_order_info` (代扣订单信息表)
+ **分表依据**: **总量 ~11.9 亿，日增 ~918 万。数据量和增速都极其巨大，是支付链路的最终落点，必须分表。**

| 业务场景 | 场景分类 | 涉及字段 (写) | `WHERE` 条件中的关键字段 (读) |
| :--- | :--- | :--- | :--- |
| **支付核心层获取/创建**   (`WithholdOrderHelperImpl#getOrCreateWithholdOrderInfo`) | 核心流程 | 全量字段 (创建时) | `front_req_no` (唯一键查询) |
| **渠道交互后更新状态**   (`WithholdOrderInnerServiceImpl#updateStatusByIdAndStatus`) | 核心流程 | `order_status`, `resp_ext_info`, `update_time`, `channel_resp_msg` 等 | `id`, `order_status` (fromStatus) |
| **订单确认定时任务**   (`ConfirmOrderTask`) | 后台/定时任务 | N/A | `order_status`, `id` |
| **支付补偿定时任务**   (`CompensateInvokePayTask`) | 后台/定时任务 | N/A | `order_status`, `id` |
| **按主键ID查询**   (`TWithholdOrderInfoDao#selectByPrimaryKey`) | 核心流程 | N/A | `id` |
| **按唯一流水号批量查**   (`ExtWithholdOrderInfoDao#getByFrontReqNos`) | 后台/管理后台 | N/A | `front_req_no` (IN) |


---

### 表 6: `payment_order` (支付主订单表) & 表 7: `payment_order_detail` (支付明细表)
+ **分表依据**: **推断分表**。这是支付网关的核心订单表，是 `t_withhold_order_info` 的直接下游，数据量级必然巨大，必须分表。

| 业务场景 | 场景分类 | 涉及字段 (写) | `WHERE` 条件中的关键字段 (读) |
| :--- | :--- | :--- | :--- |
| **支付网关统一入口创建**   (`QuickPayApplySaveHandler`) | 核心流程 | 全量字段 (创建时) | N/A |
| **幂等检查-重复请求**   (`QuickPayApplySaveHandler#dealWithForDuplicate`) | 核心流程 | N/A | `front_req_no` (唯一键查询) |
| **(推断)后台对账/查询** | 后台/管理后台 | N/A | (推断) `pay_order_no`, `channel_order_no`, `status`, `create_time` 等 |
| **(推断)异步回调更新** | 核心流程 | `status`, `channel_resp_code`, `channel_resp_msg`, `finish_time` | `id` 或 `pay_order_no` |


---

### 表 8: `t_pay_divide_detail` (分账明细表)
+ **分表依据**: **总量 ~1.17 亿，日增 ~45 万。数据量大，需要分表。**

| 业务场景 | 场景分类 | 涉及字段 (写) | `WHERE` 条件中的关键字段 (读) |
| :--- | :--- | :--- | :--- |
| **代付类交易同步创建**   (`PaymentOrderInnerServiceImpl#insertOrderByPay`) | 核心流程 (代付) | 全量字段 | N/A (写入) |
| **(推断)分账执行/查询** | 后台/定时任务 | `status`, `divide_time`, `channel_resp_msg` | `payment_order_id`, `status` |
| **(推断)OMS后台查询** | 后台/管理后台 | N/A | `payment_order_id`, `project_no`, `status` 等 |


---

_附注：_`t_pay_divide_detail`_ 表的创建不属于支付链路，而是属于其他业务的同步流程。_



# 三、基于现状的分表方案
## 分片键选择
### loanOrderId
| 所属系统 | 表名 | 存在 | 结论与改造方案 | 存量表数据为null条数 |
| :--- | :--- | :--- | :--- | :--- |
| `css-trans` | `t_biz_order` | **是** | 已有 | 0 |
| | `t_biz_order_detail` | **是** | 已有 | **** |
| | `t_trans_order` | **是** | 已有 ，但是存在NULL值。 | 6452 |
| `withhold-orders` | `t_pay_route_order` | **是** | 已有 | 0 |
| | `t_withhold_order_info` | **是** | 已有 ，但是存在NULL值。 | 109319 |
| `pay`<br/> | `t_payment_order` | **否** | **需要改造**。**关键数据链路断点**。否则数据将成为孤岛。 | **** |
| | `t_payment_order_detail` | **是** | 已有 ，但是存在NULL值。 | **52421827** |
| | `t_pay_divide_detail` | **否** | **需要改造** | **** |




### projectNo
虽然每个表都有，但是项目就是融担机构，已经通过融担分表，无法采用

| 所属系统 | 表名 | 已有 | 改造方案 |
| :--- | :--- | :--- | :--- |
| `css-trans` | `t_biz_order` | **是** | 无需改造。 |
| `css-trans` | `t_biz_order_detail` | **是** | 无需改造。 |
| `css-trans` | `t_trans_order` | **是** | 无需改造。 |
| `withhold-orders` | `t_pay_route_order` | **是** | 无需改造。 |
| `withhold-orders` | `t_withhold_order_info` | **是** | 无需改造。 |
| `pay` | `t_payment_order` | **是** | 无需改造。 |
| `pay` | `t_payment_order_detail` | **否** | **需要改造**。增加 `project_no` 字段。 |
| `pay` | `t_pay_divide_detail` | **是** | 无需改造。 |


### cust_no
数据分布性好，但数据链路**断点非常严重**，需要对下游两个系统的5张核心表进行大量的字段和代码改造，成本比较高。

| 所属系统 | 表名 | 已有 | 改造方案 |
| :--- | :--- | :--- | :--- |
| `css-trans` | `t_biz_order` | **是** | 无需改造。 |
| `css-trans` | `t_biz_order_detail` | **否** | **需要改造**。增加 `cust_no` 字段。 |
| `css-trans` | `t_trans_order` | **是** | 无需改造。 |
| `withhold-orders` | `t_pay_route_order` | **否** | **需要改造**。增加 `cust_no` 字段并改造代码。 |
| `withhold-orders` | `t_withhold_order_info` | **否** | **需要改造**。增加 `cust_no` 字段并改造代码。 |
| `pay` | `t_payment_order` | **否** | **需要改造**。增加 `cust_no` 字段并改造代码。 |
| `pay` | `t_payment_order_detail` | **否** | **需要改造**。增加 `cust_no` 字段。 |
| `pay` | `t_pay_divide_detail` | **否** | **需要改造**。增加 `cust_no` 字段。 |


## 分片算法


## 方案对比
### 自定义MyBatis拦截器 (CustomMyBatisInterceptor)
**核心思想**：在不改变现有 `DynamicDataSource`（负责分库）的前提下，额外增加一个MyBatis的`Interceptor`。该拦截器会拦截即将执行的SQL语句，根据业务代码通过`ThreadLocal`传入的分片键（如`loan_order_id`），动态地改写SQL，将逻辑表名（如 `t_trans_order`）替换为物理表名（如 `t_trans_order_58`）。

#### 优点
1. **代码侵入性极低**：这是最大的优点。它完全不触及现有复杂且稳定的分库逻辑，只是一个新增的、可插拔的组件。
2. **实现速度快**：相比引入完整的分片框架，开发一个功能单一的SQL改写拦截器，工作量小，周期短。
3. **风险可控**：由于是新增组件，可以通过配置开关轻松启用或禁用，便于测试、上线和回滚。

#### 缺点
1. **功能非常局限**：
    - **不支持复杂查询**：无法自动处理跨分片的`JOIN`查询、分页查询、范围查询等。这些需求如果出现，需要应用层编写极其复杂的代码来聚合数据，基本不可行。
    - **非分片键查询困难**：如果查询条件不包含分片键，拦截器将无法定位到具体的分表，需要进行全部分表的扫描（广播查询），这对数据库是灾难性的，且需要拦截器支持该复杂逻辑。
2. **长期维护成本高**：SQL解析逻辑是脆弱的。随着业务发展，一旦出现更复杂的SQL语法，这个自定义的拦截器就需要不断更新、测试，成为一个需要长期投入人力维护的"技术债"。
3. **缺乏治理能力**：没有配套的监控、审计、配置管理、分布式事务等高级功能，是一个临时快速解决问题的方案。

#### 迁移问题
+ **数据迁移是核心难点**：无论哪种方案，都需要将历史数据从旧的总表迁移到新的分表中。这通常需要`"双写"`过渡期：
    1. 创建所有物理分表（`t_trans_order_0` 到 `t_trans_order_N`）。
    2. 通过离线脚本将历史数据按分片键迁移到对应分表。
    3. 应用代码改造，在过渡期内，写操作同时写入旧表和新分表。
    4. 验证数据无误后，将读操作切换到新分表，并最终移除双写逻辑和旧表。
+ **应用层改造**：需要在所有需要操作分表的业务代码中，手动通过`ThreadLocal`传递分片键，存在忘记传递导致错误的风险。

### ShardingSphere-JDBC
**核心思想**：将应用的数据源从当前的`DynamicDataSource`替换为`ShardingSphereDataSource`。ShardingSphere-JDBC以Jar包形式嵌入应用，作为应用与底层数据库之间的"驱动"，全面接管数据路由、SQL改写、结果归并等所有分片相关的任务。

#### 优点
1. **功能强大且全面**：
    - 完美支持分库和分表，包括复杂查询、跨片聚合、分页、广播表等。
    - 内置支持分布式事务（XA, BASE）、数据加密、读写分离、影子库压测等企业级特性。
2. **对业务代码无侵入**：应用层只需面向逻辑表（`t_trans_order`）编写标准SQL，无需关心底层数据如何分布，也无需手动传递分片键。
3. **维护成本低**：基于声明式配置，分片逻辑与业务代码解耦。由成熟的开源社区维护，质量和功能迭代有保障。
4. **性能优异**：作为嵌入式库，它在应用的进程内运行，没有额外的网络开销，性能接近原生JDBC。

#### 缺点
1. **替换核心组件，集成风险高**：最大的问题是需要废弃现有的、经过长期验证的`DynamicDataSource`，并将它的分库逻辑（基于`counterGuaranteeNo`）和新增的分表逻辑（基于`loan_order_id`）全部在ShardingSphere中重新配置和实现。
2. **学习成本**：需要投入时间学习ShardingSphere的核心概念、配置规则和最佳实践。

#### 迁移问题
+ **"断崖式"迁移**：这是一个一次性的的切换。所有的数据源管理和路由逻辑都必须一次性在ShardingSphere中正确配置。
+ **迁移风险**：如果在切换过程中，ShardingSphere对旧分库逻辑的实现有任何偏差，都可能导致线上故障。这要求在切换前进行极其详尽和严格的测试。

### Sharding-Proxy
**核心思想**：部署一个独立的中间件服务（Sharding-Proxy），应用像连接一个普通的MySQL数据库一样连接到这个Proxy。所有的分片逻辑都在Proxy中配置，对应用完全透明。

#### 优点
1. **与应用完全解耦**：支持跨语言的异构系统，任何能连接MySQL的应用都能使用。
2. **集中化管理**：分片规则集中在Proxy层管理，运维更方便。

#### 缺点
1. **引入额外网络开销**：应用 -> Proxy -> 数据库，这条链路增加了一次网络跳跃，会带来显著的性能延迟，对于高并发、低延迟的核心系统通常是不可接受的。
2. **增加运维复杂度**：需要部署和维护一个新的高可用的中间件集群，增加了系统的故障点和运维成本。
3. **SQL兼容性限制**：Proxy需要先解析SQL才能路由，它支持的SQL语法是MySQL通用语法的子集，可能不支持某些特定或复杂的SQL用法。

#### 迁移问题
+ 与ShardingSphere-JDBC类似，但额外增加了基础设施的部署和网络连接的切换。

## 可行方案
对比来看，**自定义拦截器**方案属于"临时方案"，随着查询逻辑复杂，后期运维比较费劲。而**ShardingSphere-JDBC**方案在底层数据源的切换上无法平滑，无法失败回退，快速复原。强行进行一次性切换，风险太高。**ShardingSphere-Proxy**方案需要部署proxy服务，需要单独运维，成本较高，并且多一次网络请求，效率相对低一点。

因此，综合考虑可以使用 **两阶段演进式架构路线**：

### 第一阶段：快速解决性能瓶颈 (短期目标)
1. **行动方案**：采用 **"保留现有分库数据源 + 新增MyBatis分表拦截器"** 的方案。
2. **核心任务**：
    - 完成最困难、最耗时的物理数据迁移工作（双写、历史数据迁移）。
    - 开发并上线分表拦截器，解决`t_trans_order`等表的性能问题。
3. **达成效果**：以小的风险和快的速度解决大表的拆分问题。此时，虽然引入了技术债（自定义拦截器），但可以完成了数据层核心的改造。

### 第二阶段：构建通用的数据架构 (长期目标)
1. **行动方案**：在系统稳定运行后，规划一个独立的架构升级项目，用 **ShardingSphere-JDBC** 统一替换掉 **"原分库数据源 + 自定义分表拦截器"** 的组合。或者在原系统仅进行数据源的切换。
2. **核心任务**：
    - 将原有的分库逻辑和分表逻辑，统一用ShardingSphere的配置进行声明式实现。
    - 在应用层面，将数据源切换为`ShardingSphereDataSource`。
3. **达成效果**：
    - **风险大大降低**：因为此时数据已经是分片完成的状态，这个阶段的迁移不再涉及数据层面的操作，而是纯粹的 **"逻辑层"** 迁移，任务更清晰，风险更可控。
    - **偿还技术债**：移除了自定义维护的拦截器，将所有数据访问能力统一到业界领先的框架上。
    - **长期发展**：为系统长期发展，构建了一个健壮、可扩展、易于维护的长期数据架构。

## 表结构变更方案
### 为何不能在分表同时修改表结构？
如果我们尝试在创建分表时就直接使用和旧表不一致的新结构（例如，直接在新分表中增加字段），将在"双写"过渡期处理的非常麻烦或者无法处理：

1. **数据实体不统一**：应用层的Java实体类无法同时映射新旧两套表结构。
2. **持久化逻辑分裂**：`INSERT` 操作需要编写分裂的逻辑，向新表插入带新字段的数据，向旧表插入不带新字段的数据。这会导致DAO层代码混乱，难以维护。
3. **数据校验困难**：新旧表的数据不再是1:1的对等关系，校验数据一致性的脚本将变得异常复杂。
4. **回滚方案复杂化**：一旦线上出问题需要回滚，数据的不对等性会让回滚操作变得极其棘手和危险。

### 分阶段演进的变更流程
我们确定的正确路径如下：

1. **第一步：完成结构一致的分表 (第一阶段)**
    - **动作**: 创建所有 `t_trans_order_xx` 子表，其 DDL 严格与 `t_trans_order` **保持完全一致**。
    - **目标**: 保证在双写、迁移、校验期间，新旧表数据可以轻松地进行对等操作。此步骤的唯一目标是 **解决性能问题**，不承担任何功能迭代的任务。

```sql
1. 有分片键
  SELECT * FROM t_trans_order WHERE loan_order_id = ? AND other_condition = ?
  根据拦截器将逻辑表转为物理表，并修改SQL==>
  SELECT * FROM t_trans_order_xx WHERE loan_order_id = ? AND other_condition = ?
2. 没有分片键
  SELECT * FROM t_pay_route_order WHERE bizNo = ?
  根据拦截器将逻辑表转为物理表，并修改SQL==>
  SELECT * FROM t_trans_order_xx WHERE bizNo = ?
```

2. **第二步：在新架构上安全地变更结构 (第二阶段)**
    - **时机**: 待第一阶段完全成功，旧表下线，系统稳定运行在分表架构之上。
    - **动作**: 此时，我们可以像执行一次普通的DDL变更一样，对所有子表执行 `ALTER TABLE ... ADD COLUMN ...` 操作。
    - **优势**: 在已分片的小表上执行 `ALTER TABLE`，单次操作锁定的数据量小，执行速度快，对线上服务影响极小，风险可控。
3. 第三步：梳理代码，涉及sql语句都需要增加分表键，为切换数据源做准备。
4. 第四步：变更底层数据源管理方式，将动态多数据源替换为shardingsphere的数据源.

通过这种方式，我们将一个高风险的混合操作，分解成了两个独立的、低风险的、目标明确的操作，这是保障项目平稳上线的最佳工程实践。

# 四、数据迁移
## 数据迁移方案 (平滑切换与回滚策略)
数据迁移是整个分表项目中执行风险最高、最需要精心设计的环节。必须设计一套包含灰度、双写、校验、回滚的完整流程来确保万无一失。

我们将针对两种核心场景进行方案设计：**迁移存量数据**和**只关注增量数据**。

### 场景一：迁移存量数据
整个过程遵循 **"准备 -> 双写 -> 追历史 -> 校验 -> 切读 -> 停双写 -> 清理"** 的流程。

#### **阶段 0: 准备阶段 (上线前)**
此阶段不影响任何线上服务。

1. **创建子表**: 在所有分库中，创建与 `t_trans_order` 等结构**完全一致**的物理子表。
2. **开发迁移脚本**: 开发一个独立的、可配置的离线数据迁移脚本，确保脚本可重入、可幂等。
3. **开发校验脚本**: 开发数据校验脚本，用于后续对比新旧数据的一致性。
4. **代码准备**: 在应用程序中完成双写、切读等所有逻辑的开发，但全部通过 **动态配置开关** 控制，默认全部关闭。

#### **阶段 1: 启动双写 (上线-读旧表)**
1. **动作**: 发布已包含双写逻辑的应用程序。上线后，**开启灰度双写开关**。
2. **数据流**:
    - **写操作**: 所有新的 `INSERT`/`UPDATE` 请求，会 **同时写入** 旧的 `t_trans_order` 大表 **和** 新的 `t_trans_order_xx` 子表。
    - **读操作**: 所有 `SELECT` 请求，**仍然只读取** 旧的 `t_trans_order` 大表。
3. **回滚策略**: 如果双写逻辑出现BUG，**立即关闭双写开关**，系统恢复到只写旧表的状态，完成无损回滚。

#### **阶段 2: 迁移历史数据 (追数据)**
1. **动作**: 在双写稳定运行后，执行准备好的**离线迁移脚本，幂等性设计，可重复执行**。
2. **数据流**: 脚本从旧表中批量读取历史数据，计算分片，然后写入到对应的新子表中。
3. **回滚策略**: 此过程对线上服务无影响。如果脚本失败，修复后重新执行即可。

#### **阶段 3: 数据校验**
1. **动作**: 历史数据迁移完成后，执行数据校验脚本。
2. **校验方法**:
    - **总量对比**: 对比旧表总行数与所有新表行数之和。
    - **区间对比**: 选取双写开始前的一段历史时间，对比该区间内新旧表的数据，结果必须完全一致。
    - **抽样对比**: 随机抽取一批 `loan_order_id`，分别在新旧表中查询，逐个字段比对内容。
3. **回滚策略**: 校验过程不影响线上服务。如果校验失败，应立即暂停后续步骤，排查问题。由于主链路仍在旧表，系统依然稳定。

#### **阶段 4: 切换读流量 & 停止双写 (核心切换-读新的分表)**
1. **步骤 4.1: 切换读流量**
    - **动作**: 校验通过后，通过配置开关，将所有 `SELECT` 请求 **切换为读取新的分表**。
    - **数据流**: 此时，系统已经是"读新表、双写（新旧都写）"的状态。
    - **回滚策略**: 如果切换读后业务异常，**立即将读开关切回旧表**，系统恢复稳定，为排查问题赢得时间。
2. **步骤 4.2: 停止双写**
    - **动作**: 在"读切换"完成，并稳定运行一段时间后，通过配置开关，**关闭向旧表的写入操作**。
    - **数据流**: 旧表彻底变为"静默"状态。
    - **回滚策略**: **此步操作不可逆**，是项目成功的终点。只有在对新架构有100%信心时才可执行。

#### **阶段 5: 清理**
1. **动作**: 在停止双写后，再稳定运行一段时间。
2. **操作**: 安全地将旧的 `t_trans_order` 等表归档并 `DROP`。

### 场景二：只关注增量数据（简化方案-或者增加双写时间，让新表有足够数据）
此方案大大简化，但代价是牺牲了数据访问的统一性，适用于历史数据极少被访问的场景。

1. **定义切分时间点**: 确定一个时间点，此前的为历史数据，此后的为新数据。
2. **切换**:
    - **写操作**: 新的写入请求全部进入新的分表。
    - **读操作**: 读逻辑被改造，根据ID或时间判断，决定是从旧表读，还是从新分表读。
3. **回滚策略**: 如果新逻辑有问题，只需关闭切换开关，所有读写逻辑立刻全部回到旧表。
4. **结果**: 旧表作为巨大的"只读历史存档"被永久保留。



## 双写具体实现
"双写"是整个迁移过程中技术细节最多、风险控制要求最高的一环，需要重点关注。

### 方案A：低侵入的方案
此方案将双写逻辑清晰地实现在一个集中的业务点，风险最低，可维护性最好，但是需要修改Service和Mapper层代码。

**增加灰度发布能力**:  
	为了实现更精细化的风险控制，引入灰度发布机制。双写逻辑逐步、平滑地推向全量。

**实施步骤**:

1. **配置设计**:  
在配置中心增加两套独立的开关：
    - **双写开关**: 控制是否执行向新表的"旁路写入"。
        * `dualwrite.enable=true/false` (总开关)
        * `dualwrite.grayscale.percentage=0-100` (灰度百分比)
    - **主数据源切换开关**: 决定系统读写的主链路。
        * `read.from.sharded.table=true/false` (核心切换开关)
2. **迁移各阶段的开关状态**:
    - **初始状态**: `dualwrite.enable=false`, `read.from.sharded.table=false` (读写都在旧表)
    - **灰度双写**: `dualwrite.enable=true`, `read.from.sharded.table=false` (读旧表，双写)
    - **切换读验证**: `dualwrite.enable=true`, `read.from.sharded.table=true` (读新表，但依然双写，作为最后保险)
    - **最终状态**: `dualwrite.enable=false`, `read.from.sharded.table=true` (只读写新表，旧表被废弃)
3. **Mapper层 (XML + Java接口) - 新增，不修改**
    - `TransOrderMapper.xml`: 新增一个操作虚拟表名 `t_trans_order_sharded` 的 `insertSharded` 方法。
    - `TransOrderMapper.java`: 增加 `void insertSharded(TransOrder order);` 方法签名。
4. **Service层 - 集中修改一个方法**

```java
@Override
@Transactional
public void createNewOrder(TransOrder order) {
    // 无论读写哪个源，都需要分片键来路由
    shardingContextProvider.setShardingKey(order.getLoanOrderId());

    try {
        // 关键判断：是否已切换到新表作为主数据源？
        if (readSwitch.isFromShardedTable()) {
            // === 最终状态：只读写新表 ===
            transOrderMapper.insertSharded(order);
        } else {
            // === 过渡阶段：主链路在旧表，可选双写新表 ===
            // 1. 主业务：写入旧表
            transOrderMapper.insert(order);

            // 2. 旁路业务：检查双写开关并执行灰度双写
            if (dualWriteSwitch.isEnable() && isHitGrayscale(order.getLoanOrderId())) {
                try {
                    transOrderMapper.insertSharded(order);
                } catch (Exception e) {
                    log.error("Dual-write to sharded table failed.", e);
                }
            }
        }
    } finally {
        shardingContextProvider.clear();
    }
}
```

    - 改造核心写入方法，使其能够响应"双开关"的状态：
5. **拦截器 - 新增**
    - 新增一个 `TableShardingInterceptor`，其逻辑非常纯粹：仅当检测到上下文有分片键时，才将虚拟表名 `t_trans_order_sharded` 替换为物理分表名 `t_trans_order_xx`。

**结论**: "双开关"方案虽然增加了一个配置项，但它将迁移流程中的各个状态解耦，极大地提高了操作的安全性、可控性和可观测性，是大型系统迁移的最佳实践。

### 方案B：零侵入方案（高风险）
此方案追求对Service层的零修改，将所有复杂性都封装在MyBatis拦截器内部。开发一个全能的"自执行"拦截器，在内部自动完成原始SQL执行、派生新SQL并手动执行的全过程。

**实施步骤**:

1. **开发 **`DualWriteShardingInterceptor`:
    - 拦截目标SQL（如 `insert t_trans_order`）。
    - **执行主业务**: 首先调用 `invocation.proceed()`，让原始SQL（写旧表）执行。
    - **执行旁路业务**:
        1. 在 `try-catch` 块中进行隔离。
        2. 从当前`invocation`中获取数据库`Connection`。
        3. 根据原始SQL，手动派生出一条用于写入分表的SQL。
        4. **手动执行派生SQL**: 在拦截器内部，使用底层的JDBC API（如`PreparedStatement`），手动为SQL绑定参数并执行。

**风险**:

1. **实现极其复杂**: 需要在拦截器里编写和维护底层的JDBC代码，特别是参数绑定过程，极易出错。
2. **致命的事务缺陷**: 由于派生的SQL与原始SQL共享同一个数据库事务，一旦写入分表失败， **将会导致整个主业务事务回滚**！
3. **高昂的维护成本**: 代码逻辑晦涩，是典型的"黑魔法"，后续维护和排错将异常困难。

**结论**: 此方案虽然实现了对业务代码的"零侵入"，但其代价是引入了一个极其复杂的"黑盒"。风险高，**不推荐** 在生产环境中使用。

## 主要风险
1. **拦截器BUG**: SQL改写逻辑必须覆盖所有SQL场景，否则易出错。
2. **分片键传递遗漏**: 需要保证所有操作分表的业务链路都正确传递了`loan_order_id`。
3. **事务问题**: 无法原生支持跨物理表的事务。应从业务层面避免此类操作，梳理业务层的跨表事务。

# 五、后台业务与定时任务不包含分片键处理
如何处理不带分片键的请求？这直接决定了分表方案在"后台管理"、"定时任务"等场景下的可用性。

此问题分为 **查询请求** 和 **更新/删除请求** 两类。

## 8.1. 不带分片键的查询请求
这是最常见的场景，例如在OMS后台通过"业务流水号"等非分片键来查询交易。

### 方案A：异构索引
这是解决此类问题的比较通用的方案。

+ **核心思想**: 我们不再强迫MySQL去解决它不擅长的问题（扫描全部分表），而是将所有分表的数据，**实时同步**到一个专门用于搜索的系统**Elasticsearch (ES)**。
+ **实现流程**:
    1. **数据同步**: 通过Canal监听MySQL的Binlog，将 `t_trans_order_xx` 表中的每一条数据，都实时地写入到ES的一个索引中。
    2. **构建大宽表**: 写入ES的文档必须同时包含：分库键、**分表键 (**`loan_order_id`**)**、以及所有可能被用于查询的业务字段。
    3. **改造后台查询**: 将OMS后台的查询接口，从直接查MySQL，**改造为查询ES**。ES可以利用其强大的倒排索引，在毫秒级内返回完整的数据行。
+ **优点**: 性能极高，功能强大（支持复杂组合查询、模糊查询），将在线交易和后台查询的系统压力彻底分离。
+ **缺点**: 需要引入和维护新的技术组件（ES、Canal），增加了架构的复杂度。

### 方案B：字段映射表
+ **核心思想**: 创建一个新的、不分片的"映射表"，只包含两列或三列：`查询键` 和 `分片键` (以及可选的`分库键`)。
+ **实现流程**:
    1. **数据写入**: `INSERT`交易时，额外向这张映射表中插入一条记录，如 `('biz-seq-no-001', 'L-12345', 'CGN007')`。
    2. **改造后台查询**:
        * 第一步：先通过`查询键`查询映射表，拿到`loan_order_id`。
        * 第二步：再通过拿到的`loan_order_id`，发起一次能精确路由到分表的查询。
+ **优点**: 不引入新外部系统，实现相对简单。
+ **缺点**: 每次查询都需要两次DB交互，且只适用于"Key-Value"式的精确查询。

### 方案C：全表扫描 (无法完成，或者接入sharding后可行)
+ **核心思想**: 将一个查询请求，"广播"到当前分库下的 **所有分表** 中去执行，然后将结果在内存中进行合并。
+ **实现方式**: 程序生成一个巨大的 `UNION ALL` SQL语句发给数据库执行。
+ **优点**: 无需任何额外组件。
+ **缺点**: **性能灾难**，会对数据库造成毁灭性打击。分页和排序极难正确实现。

## 8.2. 不带分片键的更新/删除请求
**核心原则：原则上，必须从制度和技术上禁止不带分片键的**`UPDATE`**和**`DELETE`**操作。**

这是一个重要的安全红线，因为在分片架构下，此类操作的意图是模糊的，执行它的代价和风险极高（可能意外更新/删除大量数据）。

**正确的处理流程 (先查后改)**:

所有的数据订正类`UPDATE`/`DELETE`操作，都必须遵循以下两步流程：

1. **第一步：定位**
    - 操作人员必须先通过上述的 **"非分片键查询方案"**，找到目标数据行的 `loan_order_id`。
2. **第二步：执行**
    - 将这个`loan_order_id`作为分片键，连同主键（`id`）一起，构成一个精确的、能路由到唯一分表的`UPDATE`或`DELETE`语句来执行。

这种方式虽然操作上多了一步，但它能保证每一次写操作都是精准、安全、可控的，这在金融级的系统中是必须遵守的纪律。

---

# 
