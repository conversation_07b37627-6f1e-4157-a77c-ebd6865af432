@startuml 查询场景与降级策略流程图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12

title 分库分表查询场景与降级策略流程图

start

:接收查询请求;
note right: 业务层发起查询请求

:AOP拦截查询;
note right: ShardingIndexAspect拦截DAO方法

:构建查询上下文;
note right: 提取SQL、参数、分片键等信息

:检测是否包含分片键;

if (包含分片键?) then (是)
  :直接分片查询;
  note right: 利用ShardingSphere自动路由
  :查询分片表;
  :返回查询结果;
  stop
else (否)
  :进入降级策略判断;
  
  :分析查询类型;
  
  if (查询类型?) then (单点查询)
    :两阶段查询策略;
    note right: 先查索引表，再查分片表
    
    :第一阶段：查询索引表;
    note right: 根据非分片键条件查询索引表获取ID列表
    
    if (索引表命中?) then (是)
      :第二阶段：精确查询分片表;
      note right: 根据ID列表精确路由到分片表
      :返回查询结果;
      stop
    else (否)
      :记录索引表未命中;
      note right: 可能是冷数据或索引表数据不完整
      
      if (允许全表扫描?) then (是)
        :执行受限全表扫描;
        note right: 添加LIMIT限制，防止大量数据返回
        :返回查询结果;
        stop
      else (否)
        :返回空结果;
        note right: 为了保护系统性能
        stop
      endif
    endif
    
  elseif (列表查询) then
    :检查查询限制;
    
    if (查询条件复杂?) then (是)
      :拒绝查询;
      note right: 包含排序、多条件、范围查询等
      :抛出不支持异常;
      stop
    else (否)
      :强制添加LIMIT;
      note right: 限制返回条数，如最多20条
      :执行受限全表扫描;
      :记录降级查询日志;
      :返回查询结果;
      stop
    endif
    
  elseif (分页查询) then
    if (随机分页?) then (是)
      :拒绝查询;
      note right: 不支持基于OFFSET的随机分页
      :抛出不支持异常;
      stop
    else (否)
      :滚动分页查询;
      note right: 基于游标的分页方式
      
      if (分页大小超限?) then (是)
        :拒绝查询;
        note right: 分页大小超过配置限制
        :抛出参数异常;
        stop
      else (否)
        :执行滚动分页扫描;
        :返回分页结果;
        stop
      endif
    endif
    
  elseif (聚合查询) then
    :检查聚合类型;
    
    if (支持的聚合?) then (是)
      :执行聚合扫描;
      note right: 支持COUNT、SUM、AVG等基础聚合
      :返回聚合结果;
      stop
    else (否)
      :拒绝查询;
      note right: 复杂聚合查询不支持
      :抛出不支持异常;
      stop
    endif
    
  else (其他查询类型)
    :拒绝查询;
    note right: 未知或不支持的查询类型
    :抛出不支持异常;
    stop
  endif
endif

@enduml
