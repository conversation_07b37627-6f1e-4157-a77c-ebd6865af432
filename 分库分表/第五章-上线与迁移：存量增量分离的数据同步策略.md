# 第五章 - 上线与迁移：存量增量分离的数据同步策略

## 5.1 数据同步整体架构

### 5.1.1 存量增量分离模式

在分库分表的上线迁移过程中，我们采用**存量增量分离**的数据同步策略，将数据迁移问题分解为两个相对独立的子问题：

**存量数据处理：历史数据的一次性批量迁移**
- **定义**：系统上线前已存在的所有历史数据
- **特点**：数据量大、时间跨度长、数据相对稳定
- **处理方式**：通过自研迁移脚本进行批量迁移
- **时间要求**：可以在业务低峰期进行，允许较长的处理时间

**增量数据处理：新产生数据的实时双写同步**
- **定义**：迁移过程中新产生的业务数据
- **特点**：数据量相对较小、实时性要求高、需要保证业务连续性
- **处理方式**：通过双写策略实现新旧表的同步写入
- **时间要求**：必须实时处理，不能影响正常业务操作

**时间分界点设计：存量与增量的明确划分机制**
```java
/**
 * 数据同步时间分界点管理器
 * 用于明确区分存量数据和增量数据的时间边界
 */
@Component
public class DataSyncBoundaryManager {
    
    /** 迁移开始时间 - 作为存量和增量数据的分界点 */
    private volatile LocalDateTime migrationStartTime;
    
    /** 配置管理器 */
    @Autowired
    private IConfigManager configManager;
    
    /**
     * 初始化迁移分界点
     * 通常在开启双写策略时设置
     */
    public void initMigrationBoundary() {
        this.migrationStartTime = LocalDateTime.now();
        
        // 将分界点时间持久化到配置中心
        configManager.setConfig("migration.boundary.time", 
                               migrationStartTime.toString());
        
        log.info("设置数据迁移分界点: {}", migrationStartTime);
    }
    
    /**
     * 判断数据是否属于存量数据
     * @param dataCreateTime 数据创建时间
     * @return true表示存量数据，false表示增量数据
     */
    public boolean isHistoricalData(LocalDateTime dataCreateTime) {
        if (migrationStartTime == null) {
            // 如果未设置分界点，从配置中心恢复
            String boundaryTimeStr = configManager.getConfig("migration.boundary.time");
            if (boundaryTimeStr != null) {
                migrationStartTime = LocalDateTime.parse(boundaryTimeStr);
            } else {
                throw new IllegalStateException("迁移分界点未设置");
            }
        }
        
        return dataCreateTime.isBefore(migrationStartTime);
    }
    
    /**
     * 获取当前迁移分界点
     */
    public LocalDateTime getMigrationBoundary() {
        return migrationStartTime;
    }
}
```

**数据一致性保障：两种模式下的一致性策略**
- **存量数据一致性**：通过迁移后的实时校验确保数据完整性
- **增量数据一致性**：通过双写机制和冲突检测确保数据同步
- **最终一致性**：通过全量校验脚本确保新旧表数据完全一致

### 5.1.2 核心技术挑战

**单一DAO双路由问题：如何用一套DAO同时访问新旧表**

这是整个迁移方案面临的最核心技术挑战。在迁移期间，我们需要：
- 对于存量数据：从旧表读取，向新表写入
- 对于增量数据：同时向新旧表写入，根据策略从不同表读取
- 对于业务代码：保持完全透明，无需任何修改

**ShardingSphere配置冲突：逻辑表配置与物理表访问的矛盾**

ShardingSphere的配置文件中，逻辑表`t_order`被配置为指向新的分片表：
```yaml
rules:
  sharding:
    tables:
      t_order:
        actual-data-nodes: ds0.t_order_$->{0..127}
        table-strategy:
          standard:
            sharding-column: id
            sharding-algorithm-name: hash_mod
```

但在迁移期间，我们需要访问物理上的旧表`t_order`，这就产生了配置冲突。

**迁移期间数据完整性：存量迁移与增量双写的协调**

在存量数据迁移过程中，业务系统仍在正常运行，新产生的数据需要通过双写机制同步。这就要求：
- 存量迁移脚本不能影响正常业务操作
- 双写机制不能与迁移脚本产生数据冲突
- 需要有机制检测和处理迁移与双写的竞态条件

## 5.2 增量数据双写策略

### 5.2.1 HintManager路由机制

ShardingSphere提供的`HintManager`是解决双路由问题的关键技术。它是一个**线程级**的API，允许我们通过编程方式强制控制SQL的路由行为。

**线程级路由控制：基于ThreadLocal的强制路由机制**

```java
/**
 * HintManager路由控制器
 * 封装ShardingSphere HintManager的复杂操作，提供简洁的路由控制API
 */
@Component
public class ShardingHintController {
    
    private static final Logger log = LoggerFactory.getLogger(ShardingHintController.class);
    
    /**
     * 强制路由到旧表（物理表）
     * 绕过所有分片规则，直接访问名为't_order'的物理表
     * 
     * @return AutoCloseable资源，确保HintManager被正确关闭
     */
    public AutoCloseable routeToLegacyTable() {
        HintManager hintManager = HintManager.getInstance();
        
        try {
            // 设置强制路由标志，绕过分片规则
            hintManager.setWriteRouteOnly();
            
            log.debug("已设置路由到旧表的Hint");
            
            // 返回自动关闭资源
            return () -> {
                try {
                    hintManager.close();
                    log.debug("已清理旧表路由Hint");
                } catch (Exception e) {
                    log.warn("清理HintManager时发生异常", e);
                }
            };
            
        } catch (Exception e) {
            // 如果设置失败，立即关闭HintManager
            try {
                hintManager.close();
            } catch (Exception closeException) {
                log.warn("关闭HintManager时发生异常", closeException);
            }
            throw new ShardingRouteException("设置旧表路由失败", e);
        }
    }
    
    /**
     * 路由到新表（分片表）
     * 使用ShardingSphere的正常分片规则
     * 
     * @return AutoCloseable资源，确保一致的API
     */
    public AutoCloseable routeToNewTable() {
        // 新表路由使用ShardingSphere的默认行为
        // 这里主要是为了API一致性，实际不需要特殊设置
        return () -> {
            // 空实现，保持API一致性
            log.debug("新表路由无需特殊处理");
        };
    }
    
    /**
     * 设置数据库分片值
     * 用于精确控制分库路由
     * 
     * @param logicTable 逻辑表名
     * @param shardingValue 分片键值
     * @return AutoCloseable资源
     */
    public AutoCloseable setDatabaseShardingValue(String logicTable, String shardingValue) {
        HintManager hintManager = HintManager.getInstance();
        
        try {
            hintManager.addDatabaseShardingValue(logicTable, shardingValue);
            
            log.debug("已设置数据库分片值: table={}, value={}", logicTable, shardingValue);
            
            return () -> {
                try {
                    hintManager.close();
                    log.debug("已清理数据库分片值Hint");
                } catch (Exception e) {
                    log.warn("清理HintManager时发生异常", e);
                }
            };
            
        } catch (Exception e) {
            try {
                hintManager.close();
            } catch (Exception closeException) {
                log.warn("关闭HintManager时发生异常", closeException);
            }
            throw new ShardingRouteException("设置数据库分片值失败", e);
        }
    }
}
```

**配置规则覆盖：优先级高于YAML配置的编程式路由**

HintManager的核心优势在于其**最高优先级**。无论YAML配置文件中如何定义分片规则，HintManager的设置都会覆盖这些规则，实现强制路由。

**资源安全管理：try-with-resources模式的正确使用**

HintManager基于ThreadLocal实现，如果不正确关闭会导致内存泄漏。我们通过返回`AutoCloseable`接口，确保在try-with-resources块中正确使用：

```java
// 正确的使用方式
try (AutoCloseable hint = hintController.routeToLegacyTable()) {
    // 在这个代码块中，所有SQL都会路由到旧表
    legacyTableDao.save(entity);
} // hint会自动关闭，清理ThreadLocal
```

### 5.2.2 双写策略实现

双写策略通过策略模式和Spring的条件化配置实现灵活切换：

**IWriteOperationHandler接口：统一的写操作处理契约**

```java
/**
 * 写操作处理器统一接口
 * 定义了所有写操作策略的基本契约
 */
public interface IWriteOperationHandler {
    /**
     * 处理写操作
     * @param context 查询上下文，包含SQL、参数等信息
     * @return 写操作执行结果
     * @throws Throwable 写操作过程中的任何异常
     */
    Object handle(QueryContext context) throws Throwable;
    
    /**
     * 获取策略名称
     * @return 策略的唯一标识名称
     */
    String getStrategyName();
    
    /**
     * 判断是否支持指定的操作类型
     * @param operationType 操作类型
     * @return true表示支持，false表示不支持
     */
    boolean supports(OperationType operationType);
}
```

**StandardWriteStrategy：系统最终稳态的标准写入策略**

```java
/**
 * 标准写入策略 - 系统最终稳态
 * 只向新架构（分片表）执行写入操作
 */
@Component
@ConditionalOnProperty(name = "sharding-architecture.dual-write", 
                      havingValue = "false", matchIfMissing = true)
public class StandardWriteStrategy implements IWriteOperationHandler {
    
    private static final Logger log = LoggerFactory.getLogger(StandardWriteStrategy.class);
    
    /** 分片表DAO */
    @Autowired
    private IShardingTableDao shardingTableDao;
    
    /** 性能监控器 */
    @Autowired
    private IPerformanceMonitor performanceMonitor;
    
    @Override
    public Object handle(QueryContext context) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        try {
            // 直接写入分片表，利用ShardingSphere的自动路由
            Object result = shardingTableDao.execute(context.getSql(), context.getMethodArgs());
            
            // 记录成功指标
            performanceMonitor.recordWriteSuccess(getStrategyName(), 
                                                System.currentTimeMillis() - startTime);
            
            log.debug("标准写入完成: method={}, cost={}ms", 
                     context.getMethodName(), System.currentTimeMillis() - startTime);
            
            return result;
            
        } catch (Exception e) {
            // 记录失败指标
            performanceMonitor.recordWriteFailure(getStrategyName(), 
                                                System.currentTimeMillis() - startTime, 
                                                e.getClass().getSimpleName());
            
            log.error("标准写入失败: method={}, error={}", 
                     context.getMethodName(), e.getMessage());
            throw e;
        }
    }
    
    @Override
    public String getStrategyName() {
        return "StandardWrite";
    }
    
    @Override
    public boolean supports(OperationType operationType) {
        // 支持所有写操作类型
        return operationType.isWriteOperation();
    }
}
```

**DualWriteStrategy：迁移期间的双写策略**

```java
/**
 * 双写策略 - 迁移期间的过渡策略
 * 同时向新旧表写入数据，确保数据同步
 */
@Component
@ConditionalOnProperty(name = "sharding-architecture.dual-write",
                      havingValue = "true")
public class DualWriteStrategy implements IWriteOperationHandler {

    private static final Logger log = LoggerFactory.getLogger(DualWriteStrategy.class);

    /** 分片表DAO */
    @Autowired
    private IShardingTableDao shardingTableDao;

    /** 旧表DAO */
    @Autowired
    private ILegacyTableDao legacyTableDao;

    /** HintManager控制器 */
    @Autowired
    private ShardingHintController hintController;

    /** 冲突记录DAO */
    @Autowired
    private IConflictLogDao conflictLogDao;

    /** 性能监控器 */
    @Autowired
    private IPerformanceMonitor performanceMonitor;

    @Override
    public Object handle(QueryContext context) throws Throwable {
        long startTime = System.currentTimeMillis();
        OperationType operationType = determineOperationType(context);

        try {
            switch (operationType) {
                case INSERT:
                    return handleInsertOperation(context);
                case UPDATE:
                    return handleUpdateOperation(context);
                case DELETE:
                    return handleDeleteOperation(context);
                default:
                    throw new UnsupportedOperationException("不支持的操作类型: " + operationType);
            }
        } catch (Exception e) {
            // 记录失败指标
            performanceMonitor.recordWriteFailure(getStrategyName(),
                                                System.currentTimeMillis() - startTime,
                                                e.getClass().getSimpleName());
            throw e;
        }
    }

    /**
     * 处理INSERT操作 - 双写模式
     * INSERT操作会正常写入新、老两份表
     */
    private Object handleInsertOperation(QueryContext context) throws Throwable {
        // 1. 先写入旧表（使用HintManager强制路由）
        Object legacyResult = writeToLegacyTable(context);

        // 2. 再写入新表（通过ShardingSphere自动路由）
        Object newResult = writeToNewTable(context);

        log.debug("双写INSERT完成: method={}, legacyResult={}, newResult={}",
                 context.getMethodName(), legacyResult, newResult);

        // 返回新表的结果（以新表为准）
        return newResult;
    }

    /**
     * 处理UPDATE操作 - 特殊双写逻辑
     * 实现"存在即更新，不存在则记录冲突"的策略
     */
    private Object handleUpdateOperation(QueryContext context) throws Throwable {
        // 1. 先更新旧表
        Object legacyResult = writeToLegacyTable(context);

        // 2. 尝试更新新表
        Object newResult = writeToNewTable(context);

        // 3. 检查新表更新结果
        if (isUpdateMissed(newResult)) {
            // 新表中不存在该记录，记录到冲突日志表
            recordUpdateConflict(context);
            log.warn("UPDATE操作在新表中未命中: method={}, sql={}",
                    context.getMethodName(), context.getSql());
        }

        log.debug("双写UPDATE完成: method={}, legacyResult={}, newResult={}",
                 context.getMethodName(), legacyResult, newResult);

        return newResult;
    }

    /**
     * 处理DELETE操作 - 双写模式
     */
    private Object handleDeleteOperation(QueryContext context) throws Throwable {
        // 1. 先删除旧表
        Object legacyResult = writeToLegacyTable(context);

        // 2. 再删除新表
        Object newResult = writeToNewTable(context);

        log.debug("双写DELETE完成: method={}, legacyResult={}, newResult={}",
                 context.getMethodName(), legacyResult, newResult);

        return newResult;
    }

    /**
     * 写入旧表 - 使用HintManager强制路由
     */
    private Object writeToLegacyTable(QueryContext context) throws Throwable {
        try (AutoCloseable hint = hintController.routeToLegacyTable()) {
            // 强制路由到旧表，绕过所有分片规则
            return legacyTableDao.execute(context.getSql(), context.getMethodArgs());
        }
    }

    /**
     * 写入新表 - 使用ShardingSphere自动路由
     */
    private Object writeToNewTable(QueryContext context) throws Throwable {
        // ShardingSphere会根据分片键自动路由到具体分表
        return shardingTableDao.execute(context.getSql(), context.getMethodArgs());
    }

    /**
     * 判断UPDATE操作是否未命中
     */
    private boolean isUpdateMissed(Object updateResult) {
        if (updateResult instanceof Integer) {
            return ((Integer) updateResult) == 0;
        }
        return false;
    }

    /**
     * 记录UPDATE冲突
     * 将未命中的UPDATE记录到冲突日志表，由后续脚本处理
     */
    private void recordUpdateConflict(QueryContext context) {
        try {
            ConflictLogEntity conflict = new ConflictLogEntity();
            conflict.setEntityId(extractEntityId(context));
            conflict.setConflictType("UPDATE_MISS");
            conflict.setConflictTime(new Date());
            conflict.setContextInfo(context.toString());
            conflict.setSql(context.getSql());

            conflictLogDao.save(conflict);

        } catch (Exception e) {
            log.error("记录UPDATE冲突失败: {}", e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    /**
     * 从查询上下文中提取实体ID
     */
    private String extractEntityId(QueryContext context) {
        // 从SQL参数中提取ID，具体实现取决于参数结构
        Object[] args = context.getMethodArgs();
        if (args != null && args.length > 0) {
            // 简化实现，实际需要根据具体的参数结构来提取
            return String.valueOf(args[0]);
        }
        return "unknown";
    }

    /**
     * 确定操作类型
     */
    private OperationType determineOperationType(QueryContext context) {
        String sql = context.getSql().trim().toUpperCase();
        if (sql.startsWith("INSERT")) {
            return OperationType.INSERT;
        } else if (sql.startsWith("UPDATE")) {
            return OperationType.UPDATE;
        } else if (sql.startsWith("DELETE")) {
            return OperationType.DELETE;
        } else {
            throw new IllegalArgumentException("无法识别的SQL操作类型: " + sql);
        }
    }

    @Override
    public String getStrategyName() {
        return "DualWrite";
    }

    @Override
    public boolean supports(OperationType operationType) {
        return operationType.isWriteOperation();
    }
}
```

**@ConditionalOnProperty动态切换：基于配置的策略激活机制**

通过Spring的`@ConditionalOnProperty`注解，我们可以通过配置中心动态控制策略的激活：

```properties
# 双写开关配置
# true: 激活DualWriteStrategy，进入双写模式
# false或不配置: 激活StandardWriteStrategy，使用标准写入
sharding-architecture.dual-write=true
```

这种设计的优势：
1. **零重启切换**：通过配置中心可以实时切换策略
2. **安全回滚**：出现问题时可以立即回滚到标准写入模式
3. **渐进式上线**：可以先在测试环境验证双写逻辑

### 5.2.3 双写执行逻辑

**写入顺序控制：先写旧表，再写新表的安全策略**

我们采用"先写旧表，再写新表"的顺序，这样设计的原因：

1. **数据安全性**：旧表是当前生产环境的数据源，优先保证其数据完整性
2. **回滚便利性**：如果新表写入失败，旧表数据仍然完整，系统可以继续运行
3. **一致性检测**：通过对比新旧表的写入结果，可以及时发现数据不一致

**异常处理机制：单边写入失败时的处理策略**

```java
/**
 * 双写异常处理器
 * 处理双写过程中的各种异常情况
 */
@Component
public class DualWriteExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(DualWriteExceptionHandler.class);

    /** 告警管理器 */
    @Autowired
    private IAlertManager alertManager;

    /**
     * 处理旧表写入失败
     * 这是最严重的情况，需要立即告警
     */
    public void handleLegacyWriteFailure(QueryContext context, Exception e) {
        log.error("旧表写入失败 - 严重错误: method={}, sql={}, error={}",
                 context.getMethodName(), context.getSql(), e.getMessage());

        // 发送高优先级告警
        AlertMessage alert = new AlertMessage();
        alert.setLevel(AlertLevel.CRITICAL);
        alert.setTitle("旧表写入失败");
        alert.setContent(String.format("方法: %s, SQL: %s, 错误: %s",
                        context.getMethodName(), context.getSql(), e.getMessage()));
        alert.setTimestamp(new Date());

        alertManager.sendAlert(alert);

        // 旧表写入失败，直接抛出异常，阻止业务继续
        throw new DualWriteException("旧表写入失败，业务操作中止", e);
    }

    /**
     * 处理新表写入失败
     * 记录告警但不阻止业务流程
     */
    public void handleNewWriteFailure(QueryContext context, Exception e) {
        log.warn("新表写入失败 - 需要关注: method={}, sql={}, error={}",
                context.getMethodName(), context.getSql(), e.getMessage());

        // 发送普通告警
        AlertMessage alert = new AlertMessage();
        alert.setLevel(AlertLevel.WARNING);
        alert.setTitle("新表写入失败");
        alert.setContent(String.format("方法: %s, SQL: %s, 错误: %s",
                        context.getMethodName(), context.getSql(), e.getMessage()));
        alert.setTimestamp(new Date());

        alertManager.sendAlert(alert);

        // 记录到失败日志表，供后续修复
        recordWriteFailure(context, e);

        // 新表写入失败不阻止业务流程，但需要后续修复
        log.info("新表写入失败已记录，业务流程继续执行");
    }

    /**
     * 记录写入失败信息
     */
    private void recordWriteFailure(QueryContext context, Exception e) {
        try {
            WriteFailureLogEntity failureLog = new WriteFailureLogEntity();
            failureLog.setMethodName(context.getMethodName());
            failureLog.setSql(context.getSql());
            failureLog.setParameters(Arrays.toString(context.getMethodArgs()));
            failureLog.setErrorMessage(e.getMessage());
            failureLog.setErrorType(e.getClass().getSimpleName());
            failureLog.setFailureTime(new Date());
            failureLog.setStatus("PENDING"); // 待处理状态

            // 保存到失败日志表
            writeFailureLogDao.save(failureLog);

        } catch (Exception logException) {
            log.error("记录写入失败信息时发生异常: {}", logException.getMessage());
        }
    }
}
```

**事务边界管理：双写操作的原子性保障**

双写操作需要在同一个事务中完成，确保要么全部成功，要么全部失败：

```java
/**
 * 双写事务管理器
 * 确保双写操作的原子性
 */
@Component
public class DualWriteTransactionManager {

    /** 事务模板 */
    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 在事务中执行双写操作
     */
    public Object executeInTransaction(QueryContext context,
                                     Supplier<Object> dualWriteOperation) {
        return transactionTemplate.execute(status -> {
            try {
                return dualWriteOperation.get();
            } catch (Exception e) {
                // 标记事务回滚
                status.setRollbackOnly();
                throw new DualWriteException("双写操作失败，事务已回滚", e);
            }
        });
    }
}
```

**性能影响评估：双写对系统性能的影响分析**

双写策略会对系统性能产生一定影响：

1. **写入延迟增加**：需要写入两张表，理论上延迟会增加一倍
2. **数据库连接消耗**：需要更多的数据库连接来处理双写操作
3. **事务时间延长**：事务持续时间增加，可能影响并发性能

为了最小化性能影响，我们采取以下优化措施：

```java
/**
 * 双写性能优化器
 * 通过各种技术手段优化双写性能
 */
@Component
public class DualWritePerformanceOptimizer {

    /** 异步执行器 */
    @Autowired
    private AsyncTaskExecutor asyncExecutor;

    /** 批量写入缓冲区 */
    private final BatchWriteBuffer batchBuffer = new BatchWriteBuffer();

    /**
     * 异步双写优化
     * 对于非关键路径的写入，可以考虑异步处理
     */
    public CompletableFuture<Void> asyncDualWrite(QueryContext context) {
        return CompletableFuture.runAsync(() -> {
            try {
                // 执行双写操作
                performDualWrite(context);
            } catch (Exception e) {
                log.error("异步双写失败: {}", e.getMessage(), e);
                // 记录失败，但不影响主流程
            }
        }, asyncExecutor);
    }

    /**
     * 批量双写优化
     * 将多个写入操作合并为批量操作
     */
    public void batchDualWrite(List<QueryContext> contexts) {
        if (contexts.isEmpty()) {
            return;
        }

        try {
            // 批量写入旧表
            batchWriteToLegacyTable(contexts);

            // 批量写入新表
            batchWriteToNewTable(contexts);

        } catch (Exception e) {
            log.error("批量双写失败: {}", e.getMessage(), e);
            // 降级为单条处理
            contexts.forEach(this::performDualWrite);
        }
    }

    private void performDualWrite(QueryContext context) {
        // 具体的双写实现
    }

    private void batchWriteToLegacyTable(List<QueryContext> contexts) {
        // 批量写入旧表的实现
    }

    private void batchWriteToNewTable(List<QueryContext> contexts) {
        // 批量写入新表的实现
    }
}
```

### 5.2.4 双写冲突处理

在双写过程中，可能会出现各种冲突情况，需要针对不同的操作类型采用不同的处理策略：

**INSERT操作处理：新增数据的双写同步**

INSERT操作是最简单的双写场景，通常不会出现冲突：

```java
/**
 * INSERT操作双写处理器
 * 处理新增数据的双写同步逻辑
 */
@Component
public class InsertDualWriteHandler {

    private static final Logger log = LoggerFactory.getLogger(InsertDualWriteHandler.class);

    /**
     * 处理INSERT双写
     * @param context 查询上下文
     * @return 写入结果
     */
    public Object handleInsert(QueryContext context) throws Exception {
        String entityId = extractEntityId(context);

        try {
            // 1. 写入旧表
            Object legacyResult = writeToLegacyTable(context);
            log.debug("INSERT写入旧表成功: id={}, result={}", entityId, legacyResult);

            // 2. 写入新表
            Object newResult = writeToNewTable(context);
            log.debug("INSERT写入新表成功: id={}, result={}", entityId, newResult);

            // 3. 验证双写结果一致性
            validateInsertConsistency(legacyResult, newResult);

            return newResult;

        } catch (DuplicateKeyException e) {
            // 处理主键冲突
            return handleDuplicateKeyConflict(context, e);
        } catch (Exception e) {
            log.error("INSERT双写失败: id={}, error={}", entityId, e.getMessage());
            throw e;
        }
    }

    /**
     * 处理主键冲突
     * 在迁移期间，可能出现迁移脚本和双写同时插入相同数据的情况
     */
    private Object handleDuplicateKeyConflict(QueryContext context, DuplicateKeyException e) {
        String entityId = extractEntityId(context);

        log.warn("INSERT操作遇到主键冲突: id={}, 可能是迁移脚本已插入该数据", entityId);

        // 记录冲突信息
        ConflictLogEntity conflict = new ConflictLogEntity();
        conflict.setEntityId(entityId);
        conflict.setConflictType("INSERT_DUPLICATE_KEY");
        conflict.setConflictTime(new Date());
        conflict.setContextInfo(context.toString());
        conflict.setErrorMessage(e.getMessage());

        conflictLogDao.save(conflict);

        // 对于主键冲突，我们可以选择：
        // 1. 抛出异常，让业务层处理
        // 2. 转换为UPDATE操作
        // 3. 忽略冲突，返回成功

        // 这里选择转换为UPDATE操作
        return convertToUpdateOperation(context);
    }

    /**
     * 将INSERT转换为UPDATE操作
     */
    private Object convertToUpdateOperation(QueryContext context) {
        try {
            // 构造UPDATE语句
            String updateSql = convertInsertToUpdate(context.getSql());
            QueryContext updateContext = context.copy();
            updateContext.setSql(updateSql);

            log.info("将INSERT转换为UPDATE: originalSql={}, updateSql={}",
                    context.getSql(), updateSql);

            // 执行UPDATE双写
            return updateDualWriteHandler.handleUpdate(updateContext);

        } catch (Exception e) {
            log.error("INSERT转UPDATE失败: {}", e.getMessage());
            throw new DualWriteException("INSERT冲突处理失败", e);
        }
    }

    /**
     * 验证INSERT结果一致性
     */
    private void validateInsertConsistency(Object legacyResult, Object newResult) {
        // 对于INSERT操作，通常返回影响的行数
        if (legacyResult instanceof Integer && newResult instanceof Integer) {
            int legacyCount = (Integer) legacyResult;
            int newCount = (Integer) newResult;

            if (legacyCount != newCount) {
                log.warn("INSERT双写结果不一致: legacy={}, new={}", legacyCount, newCount);
                // 记录不一致情况，但不抛出异常
            }
        }
    }
}
```

**UPDATE操作处理：更新数据的双写同步**

UPDATE操作是最复杂的双写场景，需要处理各种边界情况：

```java
/**
 * UPDATE操作双写处理器
 * 处理更新数据的双写同步逻辑
 */
@Component
public class UpdateDualWriteHandler {

    private static final Logger log = LoggerFactory.getLogger(UpdateDualWriteHandler.class);

    /**
     * 处理UPDATE双写
     * 实现"存在即更新，不存在则记录冲突"的策略
     */
    public Object handleUpdate(QueryContext context) throws Exception {
        String entityId = extractEntityId(context);

        try {
            // 1. 更新旧表
            Object legacyResult = writeToLegacyTable(context);
            int legacyUpdateCount = extractUpdateCount(legacyResult);

            log.debug("UPDATE旧表完成: id={}, updateCount={}", entityId, legacyUpdateCount);

            // 2. 更新新表
            Object newResult = writeToNewTable(context);
            int newUpdateCount = extractUpdateCount(newResult);

            log.debug("UPDATE新表完成: id={}, updateCount={}", entityId, newUpdateCount);

            // 3. 分析更新结果
            analyzeUpdateResult(context, legacyUpdateCount, newUpdateCount);

            return newResult;

        } catch (Exception e) {
            log.error("UPDATE双写失败: id={}, error={}", entityId, e.getMessage());
            throw e;
        }
    }

    /**
     * 分析UPDATE结果
     * 根据新旧表的更新结果判断是否存在数据不一致
     */
    private void analyzeUpdateResult(QueryContext context, int legacyCount, int newCount) {
        String entityId = extractEntityId(context);

        if (legacyCount > 0 && newCount > 0) {
            // 理想情况：新旧表都更新成功
            log.debug("UPDATE双写成功: id={}, legacy={}, new={}", entityId, legacyCount, newCount);

        } else if (legacyCount > 0 && newCount == 0) {
            // 旧表有数据，新表没有数据 - 这是迁移期间的正常情况
            log.info("UPDATE在新表中未命中: id={}, 可能数据尚未迁移", entityId);
            recordUpdateMiss(context, "NEW_TABLE_MISS");

        } else if (legacyCount == 0 && newCount > 0) {
            // 新表有数据，旧表没有数据 - 异常情况
            log.warn("UPDATE在旧表中未命中: id={}, 数据一致性异常", entityId);
            recordUpdateMiss(context, "LEGACY_TABLE_MISS");

        } else {
            // 新旧表都没有更新到数据 - 可能是正常的业务逻辑
            log.debug("UPDATE在新旧表中都未命中: id={}", entityId);
        }
    }

    /**
     * 记录UPDATE未命中情况
     */
    private void recordUpdateMiss(QueryContext context, String missType) {
        try {
            ConflictLogEntity conflict = new ConflictLogEntity();
            conflict.setEntityId(extractEntityId(context));
            conflict.setConflictType("UPDATE_MISS_" + missType);
            conflict.setConflictTime(new Date());
            conflict.setContextInfo(context.toString());
            conflict.setSql(context.getSql());
            conflict.setParameters(Arrays.toString(context.getMethodArgs()));

            conflictLogDao.save(conflict);

        } catch (Exception e) {
            log.error("记录UPDATE未命中失败: {}", e.getMessage());
        }
    }

    /**
     * 提取UPDATE影响的行数
     */
    private int extractUpdateCount(Object updateResult) {
        if (updateResult instanceof Integer) {
            return (Integer) updateResult;
        }
        return 0;
    }
}
```

**DELETE操作处理：删除数据的双写同步**

DELETE操作相对简单，但也需要处理数据不存在的情况：

```java
/**
 * DELETE操作双写处理器
 * 处理删除数据的双写同步逻辑
 */
@Component
public class DeleteDualWriteHandler {

    private static final Logger log = LoggerFactory.getLogger(DeleteDualWriteHandler.class);

    /**
     * 处理DELETE双写
     */
    public Object handleDelete(QueryContext context) throws Exception {
        String entityId = extractEntityId(context);

        try {
            // 1. 删除旧表数据
            Object legacyResult = writeToLegacyTable(context);
            int legacyDeleteCount = extractDeleteCount(legacyResult);

            log.debug("DELETE旧表完成: id={}, deleteCount={}", entityId, legacyDeleteCount);

            // 2. 删除新表数据
            Object newResult = writeToNewTable(context);
            int newDeleteCount = extractDeleteCount(newResult);

            log.debug("DELETE新表完成: id={}, deleteCount={}", entityId, newDeleteCount);

            // 3. 分析删除结果
            analyzeDeleteResult(context, legacyDeleteCount, newDeleteCount);

            return newResult;

        } catch (Exception e) {
            log.error("DELETE双写失败: id={}, error={}", entityId, e.getMessage());
            throw e;
        }
    }

    /**
     * 分析DELETE结果
     */
    private void analyzeDeleteResult(QueryContext context, int legacyCount, int newCount) {
        String entityId = extractEntityId(context);

        if (legacyCount > 0 && newCount > 0) {
            // 理想情况：新旧表都删除成功
            log.debug("DELETE双写成功: id={}, legacy={}, new={}", entityId, legacyCount, newCount);

        } else if (legacyCount > 0 && newCount == 0) {
            // 旧表有数据，新表没有数据
            log.info("DELETE在新表中未命中: id={}, 可能数据尚未迁移", entityId);

        } else if (legacyCount == 0 && newCount > 0) {
            // 新表有数据，旧表没有数据 - 异常情况
            log.warn("DELETE在旧表中未命中: id={}, 数据一致性异常", entityId);
            recordDeleteAnomaly(context);

        } else {
            // 新旧表都没有删除到数据
            log.debug("DELETE在新旧表中都未命中: id={}", entityId);
        }
    }

    /**
     * 记录DELETE异常情况
     */
    private void recordDeleteAnomaly(QueryContext context) {
        try {
            ConflictLogEntity conflict = new ConflictLogEntity();
            conflict.setEntityId(extractEntityId(context));
            conflict.setConflictType("DELETE_ANOMALY");
            conflict.setConflictTime(new Date());
            conflict.setContextInfo(context.toString());
            conflict.setSql(context.getSql());

            conflictLogDao.save(conflict);

        } catch (Exception e) {
            log.error("记录DELETE异常失败: {}", e.getMessage());
        }
    }

    /**
     * 提取DELETE影响的行数
     */
    private int extractDeleteCount(Object deleteResult) {
        if (deleteResult instanceof Integer) {
            return (Integer) deleteResult;
        }
        return 0;
    }
}
```

**冲突检测与记录：双写不一致的识别与日志记录**

为了系统地管理双写过程中的各种冲突，我们设计了统一的冲突检测和记录机制：

```java
/**
 * 双写冲突检测器
 * 统一检测和记录双写过程中的各种冲突情况
 */
@Component
public class DualWriteConflictDetector {

    private static final Logger log = LoggerFactory.getLogger(DualWriteConflictDetector.class);

    /** 冲突记录DAO */
    @Autowired
    private IConflictLogDao conflictLogDao;

    /** 告警管理器 */
    @Autowired
    private IAlertManager alertManager;

    /**
     * 检测并记录冲突
     * @param context 查询上下文
     * @param conflictType 冲突类型
     * @param description 冲突描述
     * @param severity 严重程度
     */
    public void detectAndRecord(QueryContext context, String conflictType,
                               String description, ConflictSeverity severity) {

        // 记录冲突日志
        ConflictLogEntity conflict = new ConflictLogEntity();
        conflict.setEntityId(extractEntityId(context));
        conflict.setConflictType(conflictType);
        conflict.setDescription(description);
        conflict.setSeverity(severity.name());
        conflict.setConflictTime(new Date());
        conflict.setContextInfo(context.toString());
        conflict.setSql(context.getSql());
        conflict.setParameters(Arrays.toString(context.getMethodArgs()));
        conflict.setStatus("DETECTED");

        try {
            conflictLogDao.save(conflict);
            log.info("冲突已记录: type={}, id={}, severity={}",
                    conflictType, conflict.getEntityId(), severity);

            // 根据严重程度决定是否发送告警
            if (severity.shouldAlert()) {
                sendConflictAlert(conflict);
            }

        } catch (Exception e) {
            log.error("记录冲突失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 发送冲突告警
     */
    private void sendConflictAlert(ConflictLogEntity conflict) {
        AlertMessage alert = new AlertMessage();
        alert.setLevel(conflict.getSeverity().equals("HIGH") ? AlertLevel.WARNING : AlertLevel.INFO);
        alert.setTitle("双写冲突检测");
        alert.setContent(String.format("冲突类型: %s, 实体ID: %s, 描述: %s",
                        conflict.getConflictType(),
                        conflict.getEntityId(),
                        conflict.getDescription()));
        alert.setTimestamp(conflict.getConflictTime());

        alertManager.sendAlert(alert);
    }

    /**
     * 获取冲突统计信息
     */
    public ConflictStatistics getConflictStatistics(Date startTime, Date endTime) {
        return conflictLogDao.getStatistics(startTime, endTime);
    }
}

/**
 * 冲突严重程度枚举
 */
public enum ConflictSeverity {
    LOW(false),      // 低严重程度，不发送告警
    MEDIUM(false),   // 中等严重程度，不发送告警
    HIGH(true),      // 高严重程度，发送告警
    CRITICAL(true);  // 严重程度，立即告警

    private final boolean shouldAlert;

    ConflictSeverity(boolean shouldAlert) {
        this.shouldAlert = shouldAlert;
    }

    public boolean shouldAlert() {
        return shouldAlert;
    }
}
```

通过这套完整的双写冲突处理机制，我们可以：

1. **及时发现问题**：通过实时冲突检测，快速识别数据不一致
2. **分类处理冲突**：根据冲突类型和严重程度采用不同的处理策略
3. **保证业务连续性**：即使出现冲突，也不会中断正常的业务流程
4. **提供修复依据**：详细的冲突日志为后续的数据修复提供依据

这种设计确保了在复杂的双写场景下，系统仍能保持高可用性和数据一致性。
```
```
