@startuml UML类图样式Demo

!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 11

' 定义颜色主题
skinparam class {
    BackgroundColor #F8F9FA
    BorderColor #6C757D
    ArrowColor #495057
    AttributeFontColor #212529
    AttributeFontSize 10
}

' 接口样式
skinparam interface {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #1565C0
    FontStyle bold
}

' 抽象类样式
skinparam abstract {
    BackgroundColor #FFF3E0
    BorderColor #F57C00
    FontColor #E65100
    FontStyle italic
}

' 注解类样式
skinparam annotation {
    BackgroundColor #E8F5E8
    BorderColor #4CAF50
    FontColor #2E7D32
}

' 枚举样式
skinparam enum {
    BackgroundColor #FCE4EC
    BorderColor #E91E63
    FontColor #C2185B
}

title 第四章 - 分库分表调度引擎核心类图（样式Demo）

package "AOP切面层" #DDDDDD {
    class ShardingIndexAspect <<@Aspect>> {
        - executionEngine: IShardingExecutionEngine
        - contextBuilder: IQueryContextBuilder
        + around(ProceedingJoinPoint): Object
        - buildQueryContext(ProceedingJoinPoint): QueryContext
    }
}

package "执行引擎层" #E1F5FE {
    interface IShardingExecutionEngine {
        + execute(QueryContext): Object
    }
    
    class ShardingExecutionEngine <<@Service>> {
        - operationDetector: IOperationTypeDetector
        - writeHandler: IWriteOperationHandler
        - readHandler: IReadOperationHandler
        + execute(QueryContext): Object
        - determineOperationType(QueryContext): OperationType
    }
}

package "策略处理层" #F3E5F5 {
    interface IWriteOperationHandler {
        + handle(QueryContext): Object
        + getStrategyName(): String
        + supports(OperationType): boolean
    }
    
    class StandardWriteStrategy <<@Component>> {
        - shardingTableDao: IShardingTableDao
        - indexTableDao: IIndexTableDao
        + handle(QueryContext): Object
    }
    
    class DualWriteStrategy <<@Component>> {
        - standardWriteStrategy: StandardWriteStrategy
        - legacyTableDao: ILegacyTableDao
        - hintController: IHintManagerController
        + handle(QueryContext): Object
        - handleInsertOperation(QueryContext): Object
        - handleUpdateOperation(QueryContext): Object
    }
}

package "上下文管理" #FFF8E1 {
    class QueryContext {
        - methodName: String
        - sql: String
        - methodArgs: Object[]
        - shardingKeyValue: String
        + getSql(): String
        + hasShardingKey(): boolean
    }
    
    interface IQueryContextBuilder {
        + build(ProceedingJoinPoint): QueryContext
    }
}

package "路由控制" #E8F5E8 {
    interface IHintManagerController {
        + routeToLegacyTable(): AutoCloseable
        + routeToNewTable(): AutoCloseable
        + setDatabaseShardingValue(String): AutoCloseable
    }
    
    class SafeHintManagerOperations <<@Component>> {
        + routeToLegacyTable(): AutoCloseable
        + routeToNewTable(): AutoCloseable
        - createAutoCloseableHint(HintManager): AutoCloseable
    }
}

package "枚举定义" #FCE4EC {
    enum OperationType {
        READ
        WRITE
        INSERT
        UPDATE
        DELETE
        + isWriteOperation(): boolean
    }
    
    enum QueryStrategy {
        DIRECT_SHARDING
        INDEX_THEN_SCAN
        LIMITED_SCAN
        FORBIDDEN
    }
}

' 关系定义
ShardingIndexAspect --> IShardingExecutionEngine : uses
ShardingIndexAspect --> IQueryContextBuilder : uses
ShardingExecutionEngine ..|> IShardingExecutionEngine
ShardingExecutionEngine --> IWriteOperationHandler : uses
StandardWriteStrategy ..|> IWriteOperationHandler
DualWriteStrategy ..|> IWriteOperationHandler
DualWriteStrategy --> StandardWriteStrategy : decorates
DualWriteStrategy --> IHintManagerController : uses
SafeHintManagerOperations ..|> IHintManagerController
IQueryContextBuilder --> QueryContext : creates
ShardingExecutionEngine --> OperationType : uses

' 注释
note right of ShardingIndexAspect : AOP切面\n负责拦截DAO方法
note right of DualWriteStrategy : 装饰者模式\n包装标准写策略
note bottom of QueryContext : 封装查询上下文\n包含所有执行信息

@enduml
