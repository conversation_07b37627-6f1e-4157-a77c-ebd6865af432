@startuml 双写策略执行流程图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12

title 增量数据双写策略执行流程图

start

:接收写操作请求;
note right: 业务层发起写操作

:AOP拦截写操作;
note right: ShardingIndexAspect拦截DAO方法

:检查双写配置;
note right: sharding-architecture.dual-write

if (双写开关开启?) then (是)
  :进入双写策略;
  
  :开启事务;
  note right: 确保双写操作的原子性
  
  :判断操作类型;
  
  if (操作类型?) then (INSERT)
    :INSERT双写处理;
    
    :写入旧表;
    note right: 使用HintManager强制路由到旧表
    
    if (旧表写入成功?) then (是)
      :写入新表;
      note right: 使用ShardingSphere自动路由到分片表
      
      if (新表写入成功?) then (是)
        :提交事务;
        :记录成功指标;
        :返回写入结果;
        stop
      else (否)
        :回滚事务;
        :记录新表写入失败;
        :发送告警;
        :抛出异常;
        stop
      endif
    else (否)
      :回滚事务;
      :记录旧表写入失败;
      :发送高优先级告警;
      :抛出异常;
      stop
    endif
    
  elseif (UPDATE) then
    :UPDATE双写处理;
    
    :更新旧表;
    note right: 使用HintManager强制路由到旧表
    
    :记录旧表更新结果;
    note right: 记录影响的行数
    
    :更新新表;
    note right: 使用ShardingSphere自动路由到分片表
    
    :记录新表更新结果;
    note right: 记录影响的行数
    
    :分析更新结果;
    
    if (新旧表都更新成功?) then (是)
      :提交事务;
      :记录成功指标;
      :返回更新结果;
      stop
    elseif (旧表成功，新表失败?) then
      :记录UPDATE_MISS冲突;
      note right: 新表中可能不存在该记录
      :提交事务;
      note right: 不影响业务流程
      :返回更新结果;
      stop
    elseif (旧表失败，新表成功?) then
      :记录数据一致性异常;
      :发送告警;
      :提交事务;
      :返回更新结果;
      stop
    else (都失败)
      :提交事务;
      note right: 可能是正常的业务逻辑
      :返回更新结果;
      stop
    endif
    
  elseif (DELETE) then
    :DELETE双写处理;
    
    :删除旧表数据;
    note right: 使用HintManager强制路由到旧表
    
    :删除新表数据;
    note right: 使用ShardingSphere自动路由到分片表
    
    :分析删除结果;
    
    if (删除结果正常?) then (是)
      :提交事务;
      :记录成功指标;
      :返回删除结果;
      stop
    else (否)
      :记录删除异常;
      :提交事务;
      :返回删除结果;
      stop
    endif
    
  else (其他操作)
    :不支持的操作类型;
    :抛出异常;
    stop
  endif
  
else (否)
  :进入标准写策略;
  
  :直接写入新表;
  note right: 使用ShardingSphere自动路由到分片表
  
  if (写入成功?) then (是)
    :记录成功指标;
    :返回写入结果;
    stop
  else (否)
    :记录失败指标;
    :抛出异常;
    stop
  endif
endif

@enduml
