<svg version="1.1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 820 330.952366420201" width="820" height="330.952366420201" class="excalidraw-svg"><!-- svg-source:excalidraw --><metadata></metadata><defs><style class="style-fonts">
      @font-face { font-family: Virgil; src: url(data:font/woff2;base64,d09GMgABAAAAAA7EAAsAAAAAFyQAAA53AAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAABmAAgRQRCAqlIJteCzIAATYCJANgBCAFgxwHIBtvEVFUrppkXyTwHOrjCSdCqNqECIyGVXXsX7yN+w1P2/x3dxy0VCplBSjaX8kjp1ixmWAli0hjEVku6hNQa+jehVgBj49CFYvoGcczKHIpoHB1FQYtkK6r7fyQTivpjjXlDRiyy042OcAFwMpwDE0jtVI7IMkUEBgSLzlAC+Q94JlRfUX9n//f0anl5nxwOW0sLHttTEo8lB+taywb09awx48mYAKBrG170GcBRRSYZNBiXvWgdHTOeHUugIsWqB2gAHwC5wEA7CrUDAAmivQLGJoORw2RLQAgMDG/D8OAlqxGgfT+Nx7qakG9AQD0EQyBPsB/QkDmnyJGAMCxQpYIZ6hXgjEAKuKYMEY2mUq5VGvSwqNfjeJ/LHD14nKV6jlt3yH5lajkfrJhKJWIzZoxrXm3ADSghMgqoEwVw0eAbwoQ6gCU4JDEWdcDgzLMMmq6q3UcXqrBpyQ9kROZq+bnGQUSJyaWBXPCyp0OeeGc1C8L4J0FbBmxxRh1c/4Bmixhiw6HeDje25XOTLgIWlVH62ybIOOWCwz+rjgxTUcQdTBWj5WaVOWm3DjC/kbqrRuyLC236mqcJD08+9OTqqowIPZ6cUxG2eH16kB/bEO1P01H6fr1oq4mT/S4+x5VH2W1dYQLgOitCBB1kuZZnqH7hu0bvrWEMce4tTPEvJdAdxh16DyUFVVhyMsmUuA9RsyGHSjPuPrYmt6qT4t0GUzEaILfCLuYD0Jtmq1sK+8TjLUeFOrAQyhG6r+Kel1tp35HC+gKDfCRlO+dOWNFb4Rfwf1ZTdwlwiy09J1jumoxFKicqigU5OyaI4gp1ksMDo4RilXJi4UgdZO0dHG2yC3OLVdaEV4zBQFCfxkAcaUUA1FSpYq3kKOdaLfGFMJppYxA5f3OU/ioulblEce9Wtgny5NscvWsp7KJu4VQY11d6k2Fu+qHvWUg+ul6+isRv/5Kzh9kgW/Mj7uJxaMIRhLK3UawqpCq3BxryPeeVmU1wWtf0dxf8lykpzgPeeiQdnb3sCB3gVdhMYO3gXSSbSLWZYj+REohfc+lCU8w7unPu8Cb+PX4w21OG0dyd/Tb/GudWvvh8bvZ3nS1O9nd7kM34vgW5ksw2ujssRsBWti7WqmwANIOHF7S+/SpQc3Mssri32M2TeAv78lKYxN4JKs5xVrdzIqiPUfqE2wVscBHdMawy77MGcxA6O1gQaEAUsud4r1wELYwlVBeeNR9PQ71R1ltBB6rHTPyTGS513oWh/fyStXYKB2R5fNAMGRsnKGPouG4rOrqbFbzEItjzwOjV0V4dWT3aWePTSt9MPqo+92SR9LbGalnReuR5Z5b01vb2eF14J20A0ohP/ekTCmDYSgGXvZnsb0fMPIi24aBzMmyfG1+N939SzVJeoXWV9FvHQnnD1DLLXCOsRM6grSzWV2o66LJU7SbxdSYrQwDiOxwCrzJTbVDjUF6uYPGB+9orD8T/ZI44B0QO8oCaEVLv+Ck9QHXGofF+l6Wt5vBDLIbvkH5zWE01fsoT3fc3dYvrJfi1w3Uz8j1icXJmxW/2URBjkY4KfCwxWfN0KIdgkB4hJqNGtuue4K5aVocyjK1hgi0Fa8Dr/udFZM5f/yYiGfEwYXlAqePvHzwdR4k/iWi3ydCCA+IeIHllVGxm0YAaWR9ec/Ceidv4R52ayZ3iFhfJ+vAUyfVW7JMh9ET68nS2qDGFm4nDdk24YQ8wdpyd1PYgTSiFFI/+Gju38sp8Eqq8ne5Ic8N9THLloasBDluoT+kXrrqaCdrZxW2uRfXm+15M3ejcKVsb4m0Hx5f/4SD6qgdUBjt42tJgt2lKIJwIxc0ZvIs8GmOWi5Otjq1mrMzxPKAO8Sch5i3yeFRjdT6e7t5ppDHQDpK9+5dHOvAaCghjN6D9nQzb7/eRR4RAGSbx9ZLguXz03C+89sFOrSWpvitVjHUmH+H2kQADwgWo2DMfq2hJhcOfViS4LWdG/LM8JfeoXDQCudMYZp8l+gTsQjeL5dvWDlw3vBvTnZ38SZEv3+9fp049Q/MelbMtmEOI8ud+qVKzXP0oDbwUDdH90xXgt9LIu2LPunfbm8TdSFItEf3xHyZ+gaVsCHf8hvTOX83dfHgIyLmMQuTnv3i7/D3txk04Y0LkeXq8UQXBXyBXPgfgTL9HW1/cblk4eKyI/mP3EM7eSUB7SeND5mzV4O/zZg0sR9y2OsgBZkEsNSGCin3KhlhER92HDWEoVHwfZ0dcka20PoVsM3S7rosvDFTao5j7Om+cCBrzs4mox510godRjzBhGrlnibH6Rn6lDYSxQLfKoG+D4tdGaqjGcKUMVzMU5iw0lfCqUCYwzoD3/tGchkGN6dfHzOiIM0RMmaDXn/0BTpJZSu+CcXDvRk4+Inc9KNhSMCgk2k4KnxAVGVgMgD0ZaupKmtCz0tMctHbD99aCmamOjW6BkkXQAbxUxG2F5bdT9g3fahdgCSNCry0U589n1sNsievboY3OpsztmzBo0zPr4JKMgaT+cTDZtJRCCi7+c6AzfH3xJQlPwyysf5eY9OzsQxMqd1VfMv88PKtkmOPddIK5kS6bx+M9om4yxvb61xAdidHsrBsApqutuWWjy5skBCMMXshXTJUgNOzrQ02fxIQHuO8UHy4kdF0+GVFcmS5IDM4H+nYeEvgp0AF8D1uFtMEyUoh5SoYp148X0wItijnaJhMFsSQZXE/akVD2yfDm5oR3lNNWKlb4WE8GaZwhQB96giRkjzBIs/mq49lKO+ke2l1n7NdgLzJCH/kP0MNrf7qfwKykV63z8g0qbPrAwYk0W/M29jsVEEDkJ0PJww1g5abX/pD08pGaylo+2i4CHKyq1TrB2q29ZCQ8Yfm2KWt7dEpqaN6deuS6l31UCucqlixuhDMuPQstlYYFz7RCfwn1dxMy+YGQ8GmXaEVig4LPY3ArJ4dd/buALWrp2axxlPxs71wNRyYCM0XUIf7lyXq1SNz3MFckS/zBjTQZUH6EjQ2XFRX3pYmHKC/b5gynFcc8FJ7Qu5uSDlxENn8LF6z7FXzJ8/VOgdSGrL0W9J0Pauv0HUSTXr1deJiHdtWKv37S9prAiltRP9Uqm4JTN6aoZJvdUUB5+o2Of97TaxpPg9C+kE8Ysk82uDhhKSdHzPE9jA+Hd+A96PW+kp7+GoB3AVH5ErEWpvPm2gCunFW8Da88w9nlOCIMluPnKCpT91e/xOfDtwV5sJssRu5VIiF4nJyDTF8zTMFXi2HSGStp0CHdPGQ8M6wLkLpLpJfnH7xlOPPZbmIU2X1C+j5OqCxP5f7eCRuOFEJpVCDteLulQMC0S03FAMcX0v5rD3Gzu+KZ4G1CG9PjvfyWCmJkSr7mAQLOTEz50hqrBngM6l7Dm43PfnPakxMtK5272be8ZXpOcXSCXMRxhPVFt1IX4xAxnnHVZlCJjHG4rP73A19yWJjsQX1c7RCkPB3T1CtxlpFo16n8DpZczS+CI4WEaWC7SykEBaSmgXxlBEt4P/TRENJZHQJiY3v7enr5vHkFo40b4EkL9iPw8yhEP2vWvgYmW4AtNpejyFnx7LMdZF5ufHManIfHhw0iUFaanklJjgJeQSR9yv/Yxe49Y1XwoiKCAiOCKrRLwU0TTXFoZ/6fcoL2tnN2V/sUib16Hdzw1TbQWI+lHvAe44/tc/hrP97Oz5sIFxOUmca+zLnpbFMSijK8a4LvpuSS6mB1cA46qefgFta4jsf/pKAqR2AXaCLjc5gh6c2LzLulVop07xRJB5BcQzDR9nDmhASGfKA3NnUmNvY9YMM4pKa9trNPuZsVjpyCBK9llXhyUKWu5KlK/JfbzGXEoFkr64muPp1n9/Lb1NsY1NORGHrUuoHSbpdTSpicPV+a2pI/cfNCyGBOTxrHEbxOjxLqskKD+XFDZ91eoSCvns9WiD2B+rsj6deMdEIXR4fleUvdfD7hmTF7QzNPBwJHi0n1CRUdM6K2dTUdDtnHzPATF5BBO4yKKOIZOzqGd030y5JEp8S6Q1nbo1d1iW2oiQ5UObUPpimjsdOZGmiEH6E86tXm0Z46yJRpb22PTtlnHdySE4tb59t0QSa15vBN+eDFe+gdG97jkGHk5G8N5XdeWiIZ+dRVW5WJTS3irc03A/KdUPdsPr2Jt2lZWvAsLHvGGMTo52CUf15hg0i9UfSZdUX5xbdSFOf6oFmnIez/uCle+nakgGxPZzNssGUS4V+l7f7QQQDEYBFpdq4c+sICK5kiZf8p+8zoRUkR0PIl3iAR/NVsdP7W/zvIS9upFyGKlk8cjYNH/6NvVM97AbcPC6ciuBgX7+EiKBvZb8oXFv30Z0j2udFowjoM05tsR3bJ9RhFy/KtjN8lrjkhXW6UrKFPzSQ+X1oGz7kI1JCtTzfaHbuXqvhnN+8HGdMmpQ5UCBR9OjvF2LXvIcT/JoEG5PXRQ5zF0WLyM9k0/yAz/PU/7jjLdP6H7hZTqFuJkdGmEPuVCdNGJJsk8r1HJ8HsH4ParABsBYD3NEgWjE9qZtIQV4CANxeqD9TbPD48f2S/7/6/yv1NW4QAIAI3kJvyrrSfBcEieJmpFAQTALo357QA2DHG///P44ESAoV0GJ/wIklgBTnAHb4VtwnHsBRCVihG+SH96DUHWmxHwgJV0BxDAa88A6UhPUNOfr/YhwICNdwfj1gHk0scF+6FZyTFRoGtQCQCQogoFQSCw53sJFDqLbKYV6WyxE5nHIcngQ5yocE8AoAJl6tRWWWXydMBhdnerylWqaZLmZrfqMGMiodUAsvqFs1J0q+NBgM70zE1JK4bTJRJFUCiczvENtQz4m6Vb5Boyb9ulBtdavSRiZQuaAC4y5qjXEoW/TK5OPAo5IyafqcmRU7Q3tbBR4LKpZQHIRbXp06XSrbq7W1A66C6YLeCipQQQQAXSgA); }</style></defs><rect x="0" y="0" width="820" height="330.952366420201" fill="#ffffff"></rect><g stroke-linecap="round" transform="translate(202.8571428571429 10) rotate(0 200 30)"><path d="M15 0 C98.4 -1.87, 175.83 -1.12, 385 0 C397.76 -2.71, 400.43 8.56, 400 15 C398.15 25, 396.76 37, 400 45 C397.73 58.31, 397.22 57.97, 385 60 C261.2 58.49, 138.3 57.75, 15 60 C2.02 57.22, 0.36 57.39, 0 45 C-2.03 35.29, 1.29 33.62, 0 15 C2.2 4.24, 4.27 -0.48, 15 0" stroke="none" stroke-width="0" fill="#a5d8ff"></path><path d="M15 0 C89.53 0.13, 162.81 0.25, 385 0 M15 0 C115.76 0.42, 216.13 1.17, 385 0 M385 0 C393.7 0.53, 400.88 6.63, 400 15 M385 0 C395.2 0.56, 398.67 6.73, 400 15 M400 15 C401.4 19.78, 400.96 29.28, 400 45 M400 15 C399.32 26.37, 400.07 37.78, 400 45 M400 45 C400.6 53.4, 395.48 58.28, 385 60 M400 45 C398.31 55.06, 395.94 60.81, 385 60 M385 60 C305.85 62.13, 226.04 62.14, 15 60 M385 60 C305.02 62.56, 225.15 62.17, 15 60 M15 60 C3.11 59.71, -0.18 55.53, 0 45 M15 60 C6.83 59.98, 2.16 55.1, 0 45 M0 45 C-0.94 36.11, 1.04 26.26, 0 15 M0 45 C-0.63 37.35, 0.59 27.74, 0 15 M0 15 C0.44 7, 3.45 -1.31, 15 0 M0 15 C-0.62 2.93, 6.01 2.27, 15 0" stroke="#1e1e1e" stroke-width="2" fill="none"></path></g><g transform="translate(211.7572893415179 15) rotate(0 191.099853515625 25)"><text x="191.099853515625" y="17.619999999999997" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="20px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">DynamicDataSource → ShardingSphere-</text><text x="191.099853515625" y="42.62" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="20px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">JDBC 平滑迁移</text></g><g stroke-linecap="round" transform="translate(10 110) rotate(0 100 60)"><path d="M30 0 C64.06 -0.86, 88.42 0.35, 170 0 C192.76 -2.71, 200.43 13.56, 200 30 C197.9 49.76, 196.5 71.52, 200 90 C197.73 113.31, 192.22 117.97, 170 120 C123.54 119.46, 78.54 118.26, 30 120 C7.02 117.22, 0.36 112.39, 0 90 C-1.84 72.99, 1.48 64.02, 0 30 C2.2 9.24, 9.27 -0.48, 30 0" stroke="none" stroke-width="0" fill="#ffc9c9"></path><path d="M30 0 C58.86 0.68, 85.68 0.88, 170 0 M30 0 C68.28 -0.23, 105.93 0.99, 170 0 M170 0 C188.7 0.53, 200.88 11.63, 200 30 M170 0 C190.2 0.56, 198.67 11.73, 200 30 M200 30 C201.26 41.3, 200.82 57.33, 200 90 M200 30 C199.07 53.06, 199.82 76.15, 200 90 M200 90 C200.6 108.4, 190.48 118.28, 170 120 M200 90 C198.31 110.06, 190.94 120.81, 170 120 M170 120 C140.86 121.17, 110.64 121.19, 30 120 M170 120 C139.58 122.04, 109.33 121.4, 30 120 M30 120 C8.11 119.71, -0.18 110.53, 0 90 M30 120 C11.83 119.98, 2.16 110.1, 0 90 M0 90 C-1.16 72.51, 0.83 54.05, 0 30 M0 90 C-0.66 74.05, 0.56 56.16, 0 30 M0 30 C0.44 12, 8.45 -1.31, 30 0 M0 30 C-0.62 7.93, 11.01 2.27, 30 0" stroke="#e03131" stroke-width="2" fill="none"></path></g><g transform="translate(15 115) rotate(0 83.57596588134766 50)"><text x="0" y="14.096" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic"> 现有方案</text><text x="0" y="34.096000000000004" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic"></text><text x="0" y="54.096000000000004" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• DynamicDataSource</text><text x="0" y="74.096" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• 只支持分库</text><text x="0" y="94.096" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• 自研维护成本高</text></g><g stroke-linecap="round" transform="translate(310 109.04758998325894) rotate(0 100 60)"><path d="M30 0 C64.06 -0.86, 88.42 0.35, 170 0 C192.76 -2.71, 200.43 13.56, 200 30 C197.9 49.76, 196.5 71.52, 200 90 C197.73 113.31, 192.22 117.97, 170 120 C123.54 119.46, 78.54 118.26, 30 120 C7.02 117.22, 0.36 112.39, 0 90 C-1.84 72.99, 1.48 64.02, 0 30 C2.2 9.24, 9.27 -0.48, 30 0" stroke="none" stroke-width="0" fill="#ffd43b"></path><path d="M30 0 C58.86 0.68, 85.68 0.88, 170 0 M30 0 C68.28 -0.23, 105.93 0.99, 170 0 M170 0 C188.7 0.53, 200.88 11.63, 200 30 M170 0 C190.2 0.56, 198.67 11.73, 200 30 M200 30 C201.26 41.3, 200.82 57.33, 200 90 M200 30 C199.07 53.06, 199.82 76.15, 200 90 M200 90 C200.6 108.4, 190.48 118.28, 170 120 M200 90 C198.31 110.06, 190.94 120.81, 170 120 M170 120 C140.86 121.17, 110.64 121.19, 30 120 M170 120 C139.58 122.04, 109.33 121.4, 30 120 M30 120 C8.11 119.71, -0.18 110.53, 0 90 M30 120 C11.83 119.98, 2.16 110.1, 0 90 M0 90 C-1.16 72.51, 0.83 54.05, 0 30 M0 90 C-0.66 74.05, 0.56 56.16, 0 30 M0 30 C0.44 12, 8.45 -1.31, 30 0 M0 30 C-0.62 7.93, 11.01 2.27, 30 0" stroke="#f08c00" stroke-width="2" fill="none"></path></g><g transform="translate(315 114.04758998325894) rotate(0 54.57599639892578 50)"><text x="0" y="14.096" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic"> 核心问题</text><text x="0" y="34.096000000000004" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic"></text><text x="0" y="54.096000000000004" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• 需要分表能力</text><text x="0" y="74.096" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• 架构冲突</text><text x="0" y="94.096" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• 无法共存</text></g><g stroke-linecap="round" transform="translate(610 110) rotate(0 100 60)"><path d="M30 0 C64.06 -0.86, 88.42 0.35, 170 0 C192.76 -2.71, 200.43 13.56, 200 30 C197.9 49.76, 196.5 71.52, 200 90 C197.73 113.31, 192.22 117.97, 170 120 C123.54 119.46, 78.54 118.26, 30 120 C7.02 117.22, 0.36 112.39, 0 90 C-1.84 72.99, 1.48 64.02, 0 30 C2.2 9.24, 9.27 -0.48, 30 0" stroke="none" stroke-width="0" fill="#c3fae8"></path><path d="M30 0 C58.86 0.68, 85.68 0.88, 170 0 M30 0 C68.28 -0.23, 105.93 0.99, 170 0 M170 0 C188.7 0.53, 200.88 11.63, 200 30 M170 0 C190.2 0.56, 198.67 11.73, 200 30 M200 30 C201.26 41.3, 200.82 57.33, 200 90 M200 30 C199.07 53.06, 199.82 76.15, 200 90 M200 90 C200.6 108.4, 190.48 118.28, 170 120 M200 90 C198.31 110.06, 190.94 120.81, 170 120 M170 120 C140.86 121.17, 110.64 121.19, 30 120 M170 120 C139.58 122.04, 109.33 121.4, 30 120 M30 120 C8.11 119.71, -0.18 110.53, 0 90 M30 120 C11.83 119.98, 2.16 110.1, 0 90 M0 90 C-1.16 72.51, 0.83 54.05, 0 30 M0 90 C-0.66 74.05, 0.56 56.16, 0 30 M0 30 C0.44 12, 8.45 -1.31, 30 0 M0 30 C-0.62 7.93, 11.01 2.27, 30 0" stroke="#2f9e44" stroke-width="2" fill="none"></path></g><g transform="translate(615 115) rotate(0 88.73593139648438 50)"><text x="0" y="14.096" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic"> 解决方案</text><text x="0" y="34.096000000000004" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic"></text><text x="0" y="54.096000000000004" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• ShardingSphere-JDBC</text><text x="0" y="74.096" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• 支持分库分表</text><text x="0" y="94.096" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="16px" fill="#1e1e1e" text-anchor="start" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">• 企业级特性</text></g><g stroke-linecap="round"><g transform="translate(210 170) rotate(0 50 0)"><path d="M0.24 0.51 C17.21 0.65, 83.84 -0.05, 100.58 -0.19 M-1.09 -0.26 C15.87 0.15, 83.04 1.07, 99.93 1.24" stroke="#1e1e1e" stroke-width="2" fill="none"></path></g><g transform="translate(210 170) rotate(0 50 0)"><path d="M76.34 9.5 C81.98 8.77, 85.52 7.31, 99.93 1.24 M76.34 9.5 C83.02 6.56, 89.02 5.53, 99.93 1.24" stroke="#1e1e1e" stroke-width="2" fill="none"></path></g><g transform="translate(210 170) rotate(0 50 0)"><path d="M76.55 -7.6 C82.01 -4.91, 85.51 -2.95, 99.93 1.24 M76.55 -7.6 C83.03 -5.89, 88.97 -2.26, 99.93 1.24" stroke="#1e1e1e" stroke-width="2" fill="none"></path></g></g><mask></mask><g stroke-linecap="round"><g transform="translate(510 170) rotate(0 50 0)"><path d="M0.24 0.51 C17.21 0.65, 83.84 -0.05, 100.58 -0.19 M-1.09 -0.26 C15.87 0.15, 83.04 1.07, 99.93 1.24" stroke="#1e1e1e" stroke-width="2" fill="none"></path></g><g transform="translate(510 170) rotate(0 50 0)"><path d="M76.34 9.5 C81.98 8.77, 85.52 7.31, 99.93 1.24 M76.34 9.5 C83.02 6.56, 89.02 5.53, 99.93 1.24" stroke="#1e1e1e" stroke-width="2" fill="none"></path></g><g transform="translate(510 170) rotate(0 50 0)"><path d="M76.55 -7.6 C82.01 -4.91, 85.51 -2.95, 99.93 1.24 M76.55 -7.6 C83.03 -5.89, 88.97 -2.26, 99.93 1.24" stroke="#1e1e1e" stroke-width="2" fill="none"></path></g></g><mask></mask><g stroke-linecap="round" transform="translate(67.61910574776789 280.952366420201) rotate(0 350 20)"><path d="M10 0 C160.89 -2.38, 307.91 -1.89, 690 0 C699.43 -2.71, 700.43 6.89, 700 10 C698.24 16.75, 696.84 25.49, 700 30 C697.73 39.98, 698.89 37.97, 690 40 C462.11 37.99, 234.81 37.51, 10 40 C0.35 37.22, 0.36 39.06, 0 30 C-2.09 22.72, 1.23 23.48, 0 10 C2.2 2.58, 2.61 -0.48, 10 0" stroke="none" stroke-width="0" fill="#d0ebff"></path><path d="M10 0 C146.35 -0.17, 281.88 -0.09, 690 0 M10 0 C195.03 0.76, 379.82 1.25, 690 0 M690 0 C695.37 0.53, 700.88 4.96, 700 10 M690 0 C696.87 0.56, 698.67 5.06, 700 10 M700 10 C701.44 12.6, 701.01 19.93, 700 30 M700 10 C699.4 17.47, 700.15 24.99, 700 30 M700 30 C700.6 35.07, 697.15 38.28, 690 40 M700 30 C698.31 36.73, 697.6 40.81, 690 40 M690 40 C543.76 42.61, 397.1 42.61, 10 40 M690 40 C543.16 42.8, 396.39 42.54, 10 40 M10 40 C1.45 39.71, -0.18 37.19, 0 30 M10 40 C5.16 39.98, 2.16 36.76, 0 30 M0 30 C-0.87 23.98, 1.12 17, 0 10 M0 30 C-0.62 25.11, 0.6 18.27, 0 10 M0 10 C0.44 5.33, 1.78 -1.31, 10 0 M0 10 C-0.62 1.26, 4.35 2.27, 10 0" stroke="#1971c2" stroke-width="2" fill="none"></path></g><g transform="translate(237.6191057477679 289.702366420201) rotate(0 180 11.25)"><text x="180" y="15.858" font-family="Virgil, sans-serif, Segoe UI Emoji" font-size="18px" fill="#1e1e1e" text-anchor="middle" style="white-space: pre;" direction="ltr" dominant-baseline="alphabetic">第一阶段：平滑迁移「只做分库，不做分表」</text></g></svg>