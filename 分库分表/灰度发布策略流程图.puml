@startuml 灰度发布策略流程图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12

title 读策略灰度发布流程图

start

:接收读操作请求;
note right: 业务层发起查询请求

:AOP拦截读操作;
note right: ShardingIndexAspect拦截DAO方法

:检查读策略配置;
note right: sharding-architecture.read.path.mode

if (读策略模式?) then (sharding)
  :标准分片读策略;
  
  :检查是否包含分片键;
  
  if (包含分片键?) then (是)
    :直接查询分片表;
    :返回查询结果;
    stop
  else (否)
    :执行降级查询策略;
    note right: 参考查询场景降级流程
    :返回查询结果;
    stop
  endif
  
elseif (grayscale_migration) then
  :灰度读策略;
  
  :获取灰度百分比配置;
  note right: sharding-architecture.grayscale.percentage
  
  if (灰度百分比?) then (0%)
    :安全上线/回滚模式;
    :强制路由到旧表;
    note right: 使用HintManager路由到旧表
    :查询旧表;
    :返回查询结果;
    stop
    
  elseif (100%) then
    :观察期模式;
    :全量读取新表;
    note right: 委托给标准分片读策略
    :查询分片表;
    :返回查询结果;
    stop
    
  else (1-99%)
    :灰度期间;
    
    :提取用户标识;
    note right: 从查询参数中提取用户ID等标识
    
    if (有用户标识?) then (是)
      :用户维度一致性路由;
      
      :计算用户路由哈希;
      note right: 基于用户ID计算哈希值
      
      :判断路由目标;
      note right: 哈希值 % 100 < 灰度百分比
      
      if (路由到新表?) then (是)
        :查询新分片表;
        :记录新表查询指标;
        :返回查询结果;
        stop
      else (否)
        :查询旧表;
        :记录旧表查询指标;
        :返回查询结果;
        stop
      endif
      
    else (否)
      :随机路由;
      
      :生成随机数;
      note right: ThreadLocalRandom.nextInt(100)
      
      if (随机数 < 灰度百分比?) then (是)
        :查询新分片表;
        :记录新表查询指标;
        :返回查询结果;
        stop
      else (否)
        :查询旧表;
        :记录旧表查询指标;
        :返回查询结果;
        stop
      endif
    endif
  endif
  
else (其他模式)
  :未知读策略模式;
  :抛出配置异常;
  stop
endif

@enduml
