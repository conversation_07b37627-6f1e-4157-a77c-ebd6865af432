@startuml 第四章核心类图-简化版

!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 11

' 美化样式设置
skinparam class {
    BackgroundColor #F8F9FA
    BorderColor #6C757D
    FontColor #212529
    AttributeFontSize 10
    HeaderBackgroundColor #E9ECEF
}

skinparam interface {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #1565C0
    FontStyle bold
    HeaderBackgroundColor #BBDEFB
}

skinparam package {
    BackgroundColor #FAFAFA
    BorderColor #9E9E9E
    FontStyle bold
    FontSize 12
}

skinparam note {
    BackgroundColor #FFF3CD
    BorderColor #FFEAA7
    FontSize 10
}

title 第四章 - 分库分表调度引擎核心架构

!define LAYER1 #E3F2FD
!define LAYER2 #E8F5E8  
!define LAYER3 #FFF3E0
!define LAYER4 #F3E5F5

package "🎯 AOP切面层" LAYER1 {
    class ShardingIndexAspect {
        + around(joinPoint): Object
    }
}

package "⚙️ 执行引擎层" LAYER2 {
    interface IShardingExecutionEngine {
        + execute(context): Object
    }
    
    class ShardingExecutionEngine {
        + execute(context): Object
    }
    
    IShardingExecutionEngine <|.. ShardingExecutionEngine
}

package "📝 写策略层" LAYER3 {
    interface IWriteOperationHandler {
        + handle(context): Object
    }
    
    class StandardWriteStrategy {
        + handle(context): Object
    }
    
    class DualWriteStrategy {
        + handle(context): Object
    }
    
    IWriteOperationHandler <|.. StandardWriteStrategy
    IWriteOperationHandler <|.. DualWriteStrategy
    DualWriteStrategy o--> StandardWriteStrategy : 装饰
}

package "📖 读策略层" LAYER4 {
    interface IReadOperationHandler {
        + handle(context): Object
    }
    
    class ShardingReadStrategy {
        + handle(context): Object
    }
    
    class GrayscaleReadStrategy {
        + handle(context): Object
    }
    
    IReadOperationHandler <|.. ShardingReadStrategy
    IReadOperationHandler <|.. GrayscaleReadStrategy
    GrayscaleReadStrategy o--> ShardingReadStrategy : 装饰
}

package "🔧 支撑组件" #F5F5F5 {
    class QueryContext {
        - sql: String
        - methodArgs: Object[]
        + hasShardingKey(): boolean
    }
    
    interface IHintManagerController {
        + routeToLegacyTable(): AutoCloseable
        + routeToNewTable(): AutoCloseable
    }
}

' 主要依赖关系 - 简化版
ShardingIndexAspect --> IShardingExecutionEngine : 调用
ShardingExecutionEngine --> IWriteOperationHandler : 写操作
ShardingExecutionEngine --> IReadOperationHandler : 读操作
DualWriteStrategy --> IHintManagerController : 路由控制
GrayscaleReadStrategy --> IHintManagerController : 路由控制

' 上下文传递
ShardingIndexAspect ..> QueryContext : 创建
ShardingExecutionEngine ..> QueryContext : 使用

' 添加说明注释
note top of ShardingIndexAspect : AOP切面统一拦截\nDAO方法调用
note right of DualWriteStrategy : 装饰者模式\n实现双写逻辑
note right of GrayscaleReadStrategy : 装饰者模式\n实现灰度切换
note bottom of QueryContext : 封装查询上下文\n贯穿整个调用链

@enduml
