我有一份关于分库分表的技术方案, 内容如下:
核心内容结构
1. 项目背景与挑战
现有架构：已有基于融担号的分库体系，运行稳定
新瓶颈：库内单表膨胀，核心表如t_trans_order达到数亿级数据
核心目标：在现有分库基础上引入库内分表机制
2. 技术方案分两个阶段
第一阶段：ShardingSphere-JDBC平滑迁移
将现有DynamicDataSource替换为ShardingSphere-JDBC
保持现有分库功能不变，为分表做准备
通过编程式配置支持多配置源（Nacos + APS）
使用统一AOP路由机制和自定义Hint分片算法
第二阶段：库内分表与热数据索引构建
实施128张分表的分片策略
创建热数据索引表解决非分片键查询问题
设计完整的查询场景分级支持策略
3. 核心技术亮点
分库分表调度引擎
基于AOP的调度引擎设计，实现零侵入
支持双写策略和灰度读策略
完整的事务传播行为管理
热数据索引表方案
解决分表后非分片键查询的性能问题
基于热数据模型，只保留最近时间窗口的数据
支持动态配置的数据保留策略
上线与迁移策略
存量增量分离的数据同步策略
使用HintManager解决ShardingSphere配置冲突
自研迁移工具，支持分布式并行处理
完整的数据一致性保障机制

在文档的最后的迁移验证脚本描述的不够完善, 需要的是在描述几个部分:
1. 冲突表的的建设
要解决的问题: 1.1 在双写开始后,对于那些发生在双写开始前的insert数据的update会发生分表没有数据, 导致update结果为0导致数据状态不一致 1.2 竞态条件导致的数据状态不一致, 在迁移脚本和增量任务同时进行的时候, 假如迁移脚本先从老表读取1000条数据, 其中一条数据在读取后还没有写到新表, 在增量双写的时候发生了更新则迁移脚本会覆盖这个更新导致不一致
写入时机为: 1.1 双写过程update结果为0的时候记录 1.2 迁移过程中,每次迁移窗口的处理过程中,先比对数量,比对失败则本地迁移失败,回滚, 比对成功后逐一比对数据内容, 内容比对出错时候记录
2. 在迁移结束后从冲突表读取那些需要存在冲突的数据,从老表读, 更新到新表
3. 多重保证: 在迁移过程同时增加校验脚本, 校验数量和逐一对比, 校验已经迁移的内容, 比如正在进行5月的数据, 则校验4月的数据

你明白上面我说的问题么, 先告诉我你的理解, 然后将告诉我最后一部分你准备拟定的目录, 从问题到解决方案的方式来, 最后写入到一个新的文件, 如果你需要更多的信息请告诉我