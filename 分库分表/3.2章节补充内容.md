## 3.2 分片算法与策略设计

### 3.2.1 分片键选择策略

在分库分表架构中，分片键的选择是影响系统性能和可扩展性的核心决策。我们采用分层决策的方式来确定最优的分片策略：

#### 服务级统一分片键探索

**优先使用：** 在单个服务内部，寻找一个"黄金业务分表键"。这个分表键可以作为该服务内所有核心表的统一分片标准，这样所有的核心查询和修改都可以通过分表键精准路由到物理表。

**优势：**
- 跨表JOIN查询可以在同一分片内完成，避免跨分片查询
- 事务一致性更容易保证
- 运维和监控更加简化

**挑战：** 此类普适的"黄金分片键"通常难以找到，需要深入分析业务模式。

#### 单表级分片键决策

**降级策略：** 若确认不存在服务级统一分片键，则降级为单表级别。鉴于存量业务的复杂性，基于不改业务逻辑的原则，将分表键决策的粒度细化至每一张独立的数据表。

**决策原则：** 对于每张待分表的表，需要考虑的核心问题是："对于这张表，用哪个字段做分片键，能让最多的核心查询语句最高效？"

**评估维度：**
1. **查询频率分析** - 统计各字段在WHERE条件中的出现频率
2. **数据分布均匀性** - 确保分片键值分布相对均匀，避免热点
3. **业务语义相关性** - 分片键应与核心业务流程紧密相关
4. **扩展性考虑** - 分片键应支持未来的扩容需求

### 3.2.2 分片算法实现

#### 哈希取模算法

```java
public class HashModShardingAlgorithm implements StandardShardingAlgorithm<String> {

    private static final int SHARD_COUNT = 128;

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           ShardingValue<String> shardingValue) {
        String value = shardingValue.getValue();
        int hashCode = value.hashCode();
        int shardIndex = Math.abs(hashCode) % SHARD_COUNT;

        return findTargetByIndex(availableTargetNames, shardIndex);
    }

    private String findTargetByIndex(Collection<String> targets, int index) {
        return targets.stream()
                .filter(target -> target.endsWith("_" + index))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("No target found for index: " + index));
    }
}
```

#### 一致性哈希算法（可选）

对于需要支持动态扩容的场景，可以考虑使用一致性哈希算法：

```java
public class ConsistentHashShardingAlgorithm implements StandardShardingAlgorithm<String> {

    private final TreeMap<Long, String> ring = new TreeMap<>();
    private final int virtualNodes = 150; // 虚拟节点数

    public void addNode(String node) {
        for (int i = 0; i < virtualNodes; i++) {
            String virtualNode = node + "#" + i;
            long hash = hash(virtualNode);
            ring.put(hash, node);
        }
    }

    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           ShardingValue<String> shardingValue) {
        long hash = hash(shardingValue.getValue());
        Map.Entry<Long, String> entry = ring.ceilingEntry(hash);
        if (entry == null) {
            entry = ring.firstEntry();
        }
        return entry.getValue();
    }

    private long hash(String key) {
        // 使用MurmurHash或其他高质量哈希函数
        return key.hashCode();
    }
}
```

### 3.2.3 分片数量规划

#### 容量评估模型

基于业务增长预测和单表性能阈值，我们建立了分片数量的评估模型：

**评估公式：**
```
分片数量 = (存量数据 + 预期增长数据) / 单表理想容量
```

**具体计算：**

1. **5亿存量 + 日增300万的场景**
   - 两年后总数据量：`300万/天 × 720天 ≈ 21亿`
   - 单表承载数据量：`21亿 ÷ 128张表 ≈ 1687.5万`
   - **结论：** 单表数据量低于2000万的理想值，风险可接受

2. **10亿存量 + 日增1000万的场景**
   - 两年后总数据量：`1000万/天 × 720天 ≈ 72亿`
   - 单表承载数据量：`72亿 ÷ 128张表 ≈ 5625万`
   - **结论：** 单表数据量超过理想值，需要考虑数据生命周期管理

#### 分片数量选择原则

1. **2的幂次方原则** - 便于哈希取模和未来扩容
2. **DDL广播效率** - 分片数量不宜过多，影响DDL操作效率
3. **运维复杂度** - 平衡性能收益与运维成本
4. **硬件资源** - 考虑数据库服务器的处理能力

**最终决策：** 综合考虑上述因素，确定单库内分表数量为 **128** 张。
