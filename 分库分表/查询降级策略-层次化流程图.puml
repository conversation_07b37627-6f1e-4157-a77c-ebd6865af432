@startuml 查询降级策略-层次化流程图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12

' 美化样式
skinparam activity {
    BackgroundColor #F8F9FA
    BorderColor #6C757D
    FontColor #212529
    FontSize 11
}

skinparam diamond {
    BackgroundColor #FFF3CD
    BorderColor #F39C12
    FontColor #E67E22
    FontStyle bold
}

skinparam note {
    BackgroundColor #E8F5E8
    BorderColor #27AE60
    FontSize 10
}

title 分库分表查询降级策略（层次化）

start

:🎯 **接收查询请求**;

:🔍 **检测分片键**;

if (包含分片键?) then (✅ 是)
  #LightGreen:🚀 **直接分片查询**
  ====
  **性能最优路径**
  - 利用ShardingSphere自动路由
  - 精确定位到具体分表
  - 查询效率最高;
  stop
else (❌ 否)
  #LightYellow:⚠️ **进入降级策略**;
  
  :📊 **分析查询类型**;
  
  if (查询类型?) then (🎯 单点查询)
    #LightBlue:🔄 **两阶段查询**
    ====
    **推荐降级方案**
    ① 查询索引表获取ID
    ② 根据ID精确查询分片表;
    stop
    
  elseif (📋 简单列表) then
    #Orange:⚡ **受限扫描**
    ====
    **有条件允许**
    - 强制添加LIMIT 20
    - 禁止复杂条件
    - 记录降级日志;
    stop
    
  elseif (📄 分页查询) then
    if (随机分页?) then (是)
      #Red:🚫 **拒绝查询**
      ====
      **不支持OFFSET分页**;
      stop
    else (否)
      #LightCyan:📖 **游标分页**
      ====
      **支持有序分页**
      - 基于主键游标
      - 限制分页大小;
      stop
    endif
    
  elseif (📈 聚合查询) then
    #Purple:🧮 **基础聚合**
    ====
    **有限支持**
    - 仅支持COUNT/SUM
    - 禁止复杂GROUP BY;
    stop
    
  else (❓ 其他类型)
    #Red:🚫 **拒绝查询**
    ====
    **未知查询类型**;
    stop
  endif
endif

@enduml
