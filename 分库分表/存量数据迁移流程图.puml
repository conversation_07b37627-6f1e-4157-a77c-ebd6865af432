@startuml 存量数据迁移流程图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12

title 存量数据迁移与实时校验流程图

|迁移主流程|
start

:初始化迁移任务;
note right: 设置任务ID、表名、数据范围等

:检查或创建检查点;
note right: 支持断点续传功能

:获取数据源配置;
note right: 识别数据源类型（巨型单表/按月归档/大字段归档）

:选择读取策略;
note right: 根据数据源类型选择对应的读取策略

:获取数据范围;
note right: 确定最小ID和最大ID

:创建批次处理器;
note right: 根据数据量动态调整批次大小

|迁移执行|
while (还有未处理批次?) is (是)
  :检查系统资源;
  note right: CPU、内存、数据库连接使用率
  
  if (资源使用率过高?) then (是)
    :暂停迁移;
    note right: 等待资源释放
    :调整限流速率;
  else (否)
    :获取下一批次;
    note right: 获取批次范围[startId, endId]
    
    :读取源数据;
    note right: 根据策略从异构数据源读取
    
    if (读取成功?) then (是)
      :转换数据格式;
      note right: 转换为标准业务对象
      
      :写入目标分片表;
      note right: 根据分片键路由到对应分表
      
      if (写入成功?) then (是)
        :更新进度;
        :更新检查点;
        
        |校验子流程|
        :触发实时校验;
        note right: 迁移完成后立即校验该批次
        
        fork
          :校验数据条数;
          note right: 对比源表和目标表的记录数量
          
          if (条数一致?) then (是)
            :记录校验成功;
          else (否)
            :记录条数不一致;
            :发送告警;
          endif
        fork again
          :校验数据内容;
          note right: 逐条比对关键字段值
          
          :分批读取源数据;
          :分批读取目标数据;
          :逐条比对字段值;
          
          if (内容一致?) then (是)
            :记录校验成功;
          else (否)
            :记录差异数据;
            :生成修复脚本;
            :发送告警;
          endif
        end fork
        
        :更新校验状态;
        
        |迁移主流程|
      else (否)
        :记录写入失败;
        :更新检查点状态为失败;
        :发送告警;
        
        if (重试次数未超限?) then (是)
          :等待重试间隔;
          :重试写入;
        else (否)
          :标记批次失败;
          :继续下一批次;
        endif
      endif
    else (否)
      :记录读取失败;
      :发送告警;
      
      if (重试次数未超限?) then (是)
        :等待重试间隔;
        :重试读取;
      else (否)
        :标记批次失败;
        :继续下一批次;
      endif
    endif
  endif
endwhile (否)

:标记迁移任务完成;
:生成迁移报告;

|最终校验|
:执行全量校验;
note right: 对整个表进行最终一致性校验

:生成校验报告;

if (校验通过?) then (是)
  :迁移成功;
  stop
else (否)
  :生成修复脚本;
  :发送告警;
  :迁移完成但需要修复;
  stop
endif

@enduml
