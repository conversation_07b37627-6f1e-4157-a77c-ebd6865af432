# 3. 库内分表与热数据索引方案

## 3.1 分表的必要性

随着业务的持续高速增长，尽管现有的分库机制有效分散了整体数据压力，但新的性能瓶颈在**分库内部**浮现。部分融担数据高度集中，导致其所在的单个物理分库中，核心业务表（如 `t_trans_order`）的数据量再次达到性能极限，单表数据规模已达数亿级别。

这种库内单表的过度膨胀，已引发以下核心痛点：

+ **查询性能急剧下降：** 对大表的查询，尤其是非主键查询和分页查询，响应时间显著增长，严重影响用户体验。
+ **数据库维护困难：** 对大表的DDL操作（如加索引、改字段）耗时漫长，锁表风险高，数据库的日常维护和迭代变得异常困难。
+ **潜在的稳定性风险：** 大规模的慢查询持续消耗数据库资源，对整个分库的稳定性构成严重威胁。

**解决方案：** 在现有成熟、稳定的分库体系之上，引入**库内分表**（Table Sharding）机制。通过将单库内的大表进一步水平拆分为多张物理子表，将单点的数据压力和查询负载均匀分散。

## 3.2 分表策略设计

### 3.2.1 分片键选择

+ **分库键:** 融担号维持不变，以确保对现有分库逻辑的兼容性。
+ **库内分表键:** 
    - **优先使用：** 服务级统一分片键探索。在单个服务内部，寻找一个"黄金业务分表键"。这个分表键可以作为该服务内所有核心表的统一分片标准，这样所有的核心查询和修改都可以通过分表键精准路由物理表。然而，此类普适的"黄金分片键"通常难以找到。
    - **降级到单表：** 若确认不存在则降级为单表级别。鉴于存量业务的复杂性，基于不改业务的逻辑，将分表键的决策粒度被细化至每一张独立的数据表。对于每张待分表的表，以及对于业务的理解，需要考虑的问题是："对于这张表，用哪个字段做分片键，能让最多的核心查询语句最高效？"

通过此层决策，项目内绝大多数的表都将拥有一个最**适合自身业务场景的分片键**。

### 3.2.2 分表数量规划

+ **分表数量：** 综合考虑未来2-3年的业务增长、单表容量以及DDL广播效率，确定单库内分表数量为 **128** 张。

+ **单表数据量压力评估**
    - **5亿存量 +日增300万**
        * **两年后单库总数据量:** `300万/天 * 720天 ≈ 21亿`
        * **单表承载数据量:** `21亿 / 128张表 ≈` **1687.5万**
        * **结论:** 单表数据量低于2000万的理想值，此风险被认为是可接受的。
    - **10亿存量 + 日增 1000万**
        * **两年后单库总数据量:** `1000万/天 * 720天 ≈ 72亿`
        * **单表承载数据量:** `72亿 / 128张表 ≈` **5625万**
        * **结论:** 单表数据量高于2000万的理想值，不增加分表的前提下, 需要减少存储时长, 不过此类表较少, 目前支付模块仅trans_order表在8亿存量+900w增量左右, 其他系统未做调研。

### 3.2.3 分片算法实现

采用哈希取模算法确保数据均匀分布：

```java
/**
 * 哈希取模分片算法实现
 * 用于将数据根据分片键均匀分布到128张分表中
 */
public class HashModShardingAlgorithm implements StandardShardingAlgorithm<String> {

    /**
     * 分表数量：128张
     * 基于2-3年业务增长预估，单表数据量控制在2000万以内
     */
    private static final int SHARD_COUNT = 128;

    /**
     * 执行分片路由逻辑
     * @param availableTargetNames 可用的目标表名集合
     * @param shardingValue 分片键值对象
     * @return 目标分表名称
     */
    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           ShardingValue<String> shardingValue) {
        // 获取分片键的值（如订单ID、用户ID等）
        String value = shardingValue.getValue();

        // 计算哈希值，确保相同的分片键总是路由到同一张表
        int hashCode = value.hashCode();

        // 取绝对值并对分表数量取模，得到分表索引（0-127）
        int shardIndex = Math.abs(hashCode) % SHARD_COUNT;

        // 根据索引找到对应的物理表名
        return findTargetByIndex(availableTargetNames, shardIndex);
    }

    /**
     * 根据分表索引查找对应的物理表名
     * @param targets 所有可用的表名集合
     * @param index 分表索引（0-127）
     * @return 匹配的表名，格式如：t_order_0, t_order_1, ..., t_order_127
     */
    private String findTargetByIndex(Collection<String> targets, int index) {
        return targets.stream()
                // 过滤出以"_索引号"结尾的表名
                .filter(target -> target.endsWith("_" + index))
                .findFirst()
                // 如果找不到对应的表，抛出异常（配置错误）
                .orElseThrow(() -> new IllegalStateException("No target found for index: " + index));
    }
}
```

## 3.3 非分表键查询的性能挑战

分表后，所有不带分片键的查询将无法路由到具体的物理表，面临以下问题：

+ **全表扫描风险：** ShardingSphere在无分片键时，默认会对所有分片执行广播查询，在128个分片上并行执行，瞬间对数据库造成巨大I/O和连接池压力。
+ **性能急剧下降：** 原本的单表查询变成了128个表的并行查询，响应时间和资源消耗成倍增长。
+ **系统稳定性威胁：** 大量的广播查询可能引发数据库雪崩，严重威胁系统稳定性。

**传统解决方案的局限性：**
- 外部搜索引擎（如ES）：增加系统复杂度，存在数据一致性问题
- 冗余字段：业务侵入性强，维护成本高
- 中间件路由表：需要额外的存储和维护成本

## 3.4 热数据索引表解决方案

### 3.4.1 热数据模型

基于业务访问模式的分析，我们发现一个重要规律：**最近产生的数据具有最高的访问频率**。例如：
- 90%以上的订单查询集中在最近2周的数据
- 用户主要关注最新的交易记录和状态
- 历史数据的访问频率随时间呈指数级下降

基于这个**热数据模型**，我们设计了一个轻量级的索引表方案。

### 3.4.2 索引表设计

我们创建一张普通的单表 `t_order_index`，专门存储**非分片键到分片键的映射关系**：

```sql
-- 热数据索引表设计
-- 目的：存储非分片键到分片键的映射关系，解决分表后的查询路由问题
CREATE TABLE `t_order_index` (
  -- 主键：订单ID，与主表ID一致，同时也是分片键
  -- 通过此字段可以直接路由到具体的分表
  `id` VARCHAR(64) NOT NULL COMMENT '订单ID，与主表ID一致',

  -- 业务查询字段：存储常用的非分片键查询条件
  `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
  `order_no` VARCHAR(64) NOT NULL COMMENT '订单号',
  `phone` VARCHAR(20) COMMENT '手机号',

  -- 时间字段：用于热数据管理和数据清理
  `create_time` DATETIME NOT NULL COMMENT '创建时间',

  -- 主键索引：基于分片键，支持精确路由
  PRIMARY KEY (`id`),

  -- 业务查询索引：支持各种非分片键查询
  INDEX `idx_user_id` (`user_id`),      -- 用户维度查询
  INDEX `idx_order_no` (`order_no`),    -- 订单号查询
  INDEX `idx_phone` (`phone`),          -- 手机号查询
  INDEX `idx_create_time` (`create_time`) -- 时间范围查询和数据清理
) ENGINE=InnoDB COMMENT='订单热数据索引表';
```

**设计要点：**
- **普通单表结构**：无分区，结构简单，维护方便
- **映射关系存储**：存储业务查询中常用的非分片键字段
- **主键就是分片键**：通过ID可以直接路由到具体分表
- **精简字段**：只存储查询必需的字段，保持表结构轻量

### 3.4.3 查询流程

所有非分片键查询都通过两阶段查询完成：

1. **第一阶段：索引表查询**
   ```sql
   -- 根据用户ID查询获取订单ID列表
   -- 利用索引表的idx_user_id索引，快速定位到用户的所有订单ID
   -- 这一步查询的是普通单表，性能很高
   SELECT id FROM t_order_index WHERE user_id = 'user123';
   ```

2. **第二阶段：精确分表查询**
   ```sql
   -- 根据ID精确路由到具体分表查询完整数据
   -- ShardingSphere根据ID（分片键）自动路由到对应的物理分表
   -- 避免了128个分表的广播查询，只查询包含这些ID的具体分表
   SELECT * FROM t_order WHERE id IN ('order1', 'order2', 'order3');
   ```

**性能优势：**
- 索引表查询：基于索引的高效查询，毫秒级响应
- 分表查询：通过分片键精确路由，避免广播查询
- 整体性能：两次精确查询的总耗时远低于128个分片的广播查询

## 3.5 索引表的生命周期管理

### 3.5.1 数据保留策略与热度统计

+ **动态配置的时间窗口：** 索引表只保留最近一段时间的热数据，通过配置中心动态设置保留时间。
+ **热度统计机制：**
  - 可选开启的查询热度统计功能
  - 记录不同时间范围内的查询频率分布
  - 为时间窗口优化提供数据支撑
  - 通过配置控制是否启用，避免对性能产生影响

+ **配置化管理：** 通过配置中心统一管理数据保留时间，支持动态调整。

```properties
# 索引表数据保留配置
# 热数据保留天数：只保留最近14天的数据，超过的自动清理
index.table.retention.days=14

# 数据清理调度：每天凌晨2点执行清理任务
index.table.cleanup.schedule=0 2 * * *

# 查询热度统计开关：是否启用查询热度统计功能
index.table.stats.enabled=true

# 统计采样率：1%的查询会被统计，避免对性能产生影响
index.table.stats.sample.rate=0.01
```

**热度统计实现：**

```java
/**
 * 查询热度统计收集器
 * 用于统计不同时间范围内的查询频率分布，为索引表时间窗口优化提供数据支撑
 */
@Component
public class QueryHeatStatisticsCollector {

    /**
     * 统计功能开关：是否启用查询热度统计
     * 默认关闭，避免对生产环境性能产生影响
     */
    @Value("${index.table.stats.enabled:false}")
    private boolean statsEnabled;

    /**
     * 采样率：控制统计的查询比例
     * 默认1%，在性能和统计准确性之间取得平衡
     */
    @Value("${index.table.stats.sample.rate:0.01}")
    private double sampleRate;

    /**
     * 记录查询操作，用于热度统计
     * @param queryTime 查询的数据时间（不是查询发起时间）
     */
    public void recordQuery(Date queryTime) {
        // 如果统计功能未开启或不在采样范围内，直接返回
        if (!statsEnabled || !shouldSample()) {
            return;
        }

        // 计算查询数据距离当前时间的天数
        int daysAgo = calculateDaysFromNow(queryTime);
        // 增加对应天数的查询计数
        incrementQueryCount(daysAgo);
    }

    /**
     * 判断当前查询是否应该被统计（基于采样率）
     * @return true表示应该统计，false表示跳过
     */
    private boolean shouldSample() {
        return Math.random() < sampleRate;
    }

    /**
     * 定期分析查询热度分布并推荐最优时间窗口
     * 每天凌晨1点执行，避开业务高峰期
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void analyzeAndRecommend() {
        if (!statsEnabled) {
            return;
        }

        // 获取查询时间分布统计
        Map<Integer, Long> distribution = getQueryDistribution();
        // 基于统计数据计算推荐的保留天数
        int recommendedDays = calculateOptimalRetentionDays(distribution);
        log.info("Recommended retention days: {}", recommendedDays);
    }
}
```

### 3.5.2 定时清理脚本

设计自动化的数据清理机制：

```sql
-- 索引表数据清理脚本
-- 删除超过保留期的热数据，释放存储空间
-- 使用LIMIT控制每次删除的数量，避免长时间锁表
DELETE FROM t_order_index
WHERE create_time < DATE_SUB(NOW(), INTERVAL ${retention_days} DAY)
LIMIT 10000;  -- 每次最多删除1万条记录，分批执行
```

**清理策略：**
- **分批删除：** 每次删除固定数量的记录，避免长时间锁表
- **定时执行：** 在业务低峰期执行，减少对线上业务的影响
- **监控告警：** 监控清理脚本的执行状态，确保数据生命周期管理正常

### 3.5.3 查询场景与降级策略

#### 热数据索引表的固有局限性

热数据索引表虽然有效解决了分表后的查询路由问题，但其设计也带来了一些固有的局限性：

+ **时间窗口限制**：只能查询时间窗口内的热数据，超出范围的历史数据无法通过索引表快速定位
+ **查询模式受限**：为了保持索引表的简洁性和高性能，只能支持有限的查询模式
+ **存储成本**：需要额外的存储空间来维护索引映射关系

#### 按增删改查划分的支持场景

基于业务操作类型和返回值特征，我们对查询场景进行了精确的分级支持定义：

**增（INSERT）操作**
- ✅ **完全支持**：同时更新分表和索引表，保证数据一致性

**改（UPDATE）操作**
- ✅ **完全支持**：同时更新分表和索引表，保证数据一致性

**查（SELECT）操作 - 分级支持（重点关注不带分表键的场景）**

*注：带分表键的查询是基础功能，必须完全支持，此处重点说明不带分表键的查询处理策略*

#### 不带分表键查询的分级处理

**第一级：单个对象返回（单点查询）**
- **场景**：`WHERE user_id = 'U123'` 返回单个Order对象
- **处理策略**：
  1. 先查询索引表获取分表键
  2. 若命中，直接查询对应分表
  3. 若未命中，执行有限制的全表扫描
- **性能特点**：大部分情况下性能优异

**第二级：列表返回（多条数据）**
- **场景**：`WHERE user_id = 'U123'` 返回List<Order>
- **处理策略**：
  - 强制增加LIMIT限制（默认20条，可配置）
  - 打印警告日志记录此类查询
  - 执行全表扫描获取结果
- **限制说明**：防止大批量数据查询影响系统性能

**第三级：聚合查询**
- **场景**：`SELECT COUNT(*) WHERE user_id = 'U123'`
- **处理策略**：支持基础聚合操作（COUNT、SUM、AVG等）
- **性能特点**：聚合查询性能相对较高，可以接受

**第四级：受限的分页查询**
- **滚动分页**：
  - ✅ **有限支持**：分页大小最多20条，超过报错
  - 不走索引表，直接全表扫描
  - 适用于数据浏览场景
- **随机分页**：
  - ❌ **不支持**：基于OFFSET的分页会导致性能灾难

**完全不支持的查询类型**
- ❌ **排序查询**：`WHERE user_id = 'U123' ORDER BY create_time`（无分表键的排序）
- ❌ **复合条件查询**：`WHERE user_id = 'U123' AND phone = '138xxx'`（多个非分表键字段）
- ❌ **多表查询**：涉及JOIN操作的查询
- ❌ **子查询**：包含嵌套查询的复杂SQL
- ❌ **范围查询**：`WHERE create_time BETWEEN '2024-01-01' AND '2024-01-31'`
- ❌ **模糊查询**：`WHERE order_no LIKE 'O2024%'`

**历史数据查询（时间窗口外）**
- **处理方式**：提示用户数据超出查询范围
- **替代方案**：引导用户使用数据仓库或离线查询服务

#### 分级查询策略的技术实现

```java
/**
 * 查询策略管理器
 * 根据查询上下文决定使用哪种查询策略，控制查询的执行方式和资源消耗
 */
@Component
public class QueryStrategyManager {

    /**
     * 列表查询最大返回条数限制
     * 防止无分片键的列表查询返回过多数据影响性能
     */
    @Value("${query.list.max.limit:20}")
    private int maxListLimit;

    /**
     * 分页查询最大页面大小限制
     * 控制分页查询的单页数据量，避免大页面查询
     */
    @Value("${query.pagination.max.size:20}")
    private int maxPaginationSize;

    /**
     * 全表扫描超时时间（秒）
     * 防止慢查询长时间占用数据库资源
     */
    @Value("${query.scan.timeout.seconds:10}")
    private int scanTimeoutSeconds;

    /**
     * 根据查询上下文确定查询策略
     * @param context 查询上下文，包含SQL、返回类型、查询条件等信息
     * @return 对应的查询策略枚举
     */
    public QueryStrategy determineStrategy(QueryContext context) {
        // 1. 优先检查是否包含分表键
        if (context.hasShardingKey()) {
            // 包含分表键的查询直接路由到具体分表，性能最优
            return QueryStrategy.DIRECT_SHARDING;
        }

        // 2. 不包含分表键的查询，根据返回值类型确定处理策略
        switch (context.getReturnType()) {
            case SINGLE_OBJECT:
                // 单对象查询：先查索引表，未命中则全表扫描
                return handleSingleObjectQuery(context);
            case LIST:
                // 列表查询：强制限制返回条数，全表扫描
                return handleListQuery(context);
            case PAGE:
                // 分页查询：区分滚动分页和随机分页
                return handlePageQuery(context);
            case AGGREGATION:
                // 聚合查询：支持基础的COUNT、SUM等操作
                return handleAggregationQuery(context);
            default:
                // 未知查询类型，禁止执行
                return QueryStrategy.FORBIDDEN;
        }
    }

    /**
     * 处理单对象查询策略
     * @param context 查询上下文
     * @return 查询策略
     */
    private QueryStrategy handleSingleObjectQuery(QueryContext context) {
        // 检查是否为禁止的查询类型（如排序、多条件等）
        if (isForbiddenQueryType(context)) {
            return QueryStrategy.FORBIDDEN;
        }

        // 单点查询策略：先查索引表获取分片键，未命中则执行全表扫描
        return QueryStrategy.INDEX_THEN_SCAN;
    }

    /**
     * 处理列表查询策略
     * @param context 查询上下文
     * @return 查询策略
     */
    private QueryStrategy handleListQuery(QueryContext context) {
        // 检查是否为禁止的查询类型
        if (isForbiddenQueryType(context)) {
            return QueryStrategy.FORBIDDEN;
        }

        // 列表查询策略：强制添加LIMIT限制，防止返回过多数据
        context.setForcedLimit(maxListLimit);
        // 记录警告日志，便于监控和优化
        logWarning("List query without sharding key", context);
        return QueryStrategy.LIMITED_SCAN;
    }

    /**
     * 处理分页查询策略
     * @param context 查询上下文
     * @return 查询策略
     */
    private QueryStrategy handlePageQuery(QueryContext context) {
        // 不支持基于OFFSET的随机分页，会导致性能灾难
        if (context.isRandomPagination()) {
            return QueryStrategy.FORBIDDEN;
        }

        // 检查分页大小是否超过限制
        if (context.getPageSize() > maxPaginationSize) {
            throw new QueryException("分页大小超过限制: " + maxPaginationSize);
        }

        // 支持滚动分页：基于游标的分页方式
        return QueryStrategy.SCROLL_SCAN;
    }

    /**
     * 处理聚合查询策略
     * @param context 查询上下文
     * @return 查询策略
     */
    private QueryStrategy handleAggregationQuery(QueryContext context) {
        // 检查是否为禁止的查询类型
        if (isForbiddenQueryType(context)) {
            return QueryStrategy.FORBIDDEN;
        }

        // 聚合查询策略：支持COUNT、SUM、AVG等基础聚合操作
        return QueryStrategy.AGGREGATION_SCAN;
    }

    /**
     * 判断是否为禁止的查询类型
     * 这些查询类型在分表环境下会导致严重的性能问题
     * @param context 查询上下文
     * @return true表示禁止执行，false表示允许执行
     */
    private boolean isForbiddenQueryType(QueryContext context) {
        return context.hasOrderBy() ||           // 排序查询（无分表键）：会导致全表扫描后排序
               context.hasMultipleConditions() || // 复合条件查询：多个非分表键字段组合查询
               context.hasRangeCondition() ||     // 范围查询：如时间范围查询
               context.hasLikeCondition() ||      // 模糊查询：如LIKE操作
               context.hasJoin() ||               // 多表查询：JOIN操作在分表环境下复杂度极高
               context.hasSubQuery();             // 子查询：嵌套查询难以优化
    }

    /**
     * 记录查询警告日志
     * 用于监控和分析非最优查询的使用情况
     * @param message 警告消息
     * @param context 查询上下文
     */
    private void logWarning(String message, QueryContext context) {
        // 记录详细的警告信息，包括SQL和调用方法
        log.warn("{}: SQL={}, Method={}", message, context.getSql(), context.getMethodName());
        // 同时发送到监控系统，便于统计和告警
        queryMonitor.recordWarning(message, context);
    }
}

/**
 * 查询策略枚举
 * 定义了不同场景下的查询执行策略
 */
enum QueryStrategy {
    DIRECT_SHARDING,    // 直接查询分表（带分表键）- 性能最优
    INDEX_THEN_SCAN,    // 先查索引表，未命中则扫描（单点查询）- 大部分情况性能良好
    LIMITED_SCAN,       // 有限制的全表扫描（列表查询）- 有性能风险，需要限制
    SCROLL_SCAN,        // 滚动分页扫描 - 支持基础分页功能
    AGGREGATION_SCAN,   // 聚合查询扫描 - 支持统计类查询
    FORBIDDEN           // 禁止查询 - 会导致严重性能问题的查询类型
}

/**
 * 查询返回类型枚举
 * 用于区分不同的查询返回值类型，以便采用不同的处理策略
 */
enum ReturnType {
    SINGLE_OBJECT,      // 单个对象 - 如根据ID查询单个订单
    LIST,              // 列表 - 如查询用户的所有订单
    PAGE,              // 分页对象 - 如分页查询订单列表
    AGGREGATION        // 聚合结果 - 如统计订单数量、金额等
}

/**
 * 查询上下文类
 * 封装查询相关的所有信息，用于查询策略的决策
 */
public class QueryContext {
    /** 原始SQL语句 */
    private String sql;

    /** 调用的方法名，用于日志记录和问题追踪 */
    private String methodName;

    /** 查询返回类型 */
    private ReturnType returnType;

    /** 是否包含分表键 */
    private boolean hasShardingKey;

    /** 分页查询的页面大小 */
    private int pageSize;

    /** 是否为随机分页（基于OFFSET） */
    private boolean isRandomPagination;

    /** 强制设置的LIMIT值，用于控制返回数据量 */
    private int forcedLimit;

    // 查询特征检查方法 - 用于判断查询的复杂度和风险

    /** 检查是否包含ORDER BY子句（无分表键的排序查询性能很差） */
    public boolean hasOrderBy() { /* 实现SQL解析逻辑 */ }

    /** 检查是否有多个WHERE条件（复合条件查询难以优化） */
    public boolean hasMultipleConditions() { /* 实现SQL解析逻辑 */ }

    /** 检查是否有范围查询条件（如BETWEEN、>、< 等） */
    public boolean hasRangeCondition() { /* 实现SQL解析逻辑 */ }

    /** 检查是否有LIKE模糊查询（无法使用索引） */
    public boolean hasLikeCondition() { /* 实现SQL解析逻辑 */ }

    /** 检查是否有JOIN操作（分表环境下JOIN复杂度极高） */
    public boolean hasJoin() { /* 实现SQL解析逻辑 */ }

    /** 检查是否有子查询（嵌套查询难以在分表环境下优化） */
    public boolean hasSubQuery() { /* 实现SQL解析逻辑 */ }

    // getter/setter方法...
}
```

**查询监控统计：**

```java
/**
 * 查询监控组件
 * 用于统计不同查询策略的使用情况，监控系统性能和查询质量
 */
@Component
public class QueryMonitor {

    /** 直接分表查询计数器 - 性能最优的查询方式 */
    private final Counter directShardingCounter;

    /** 索引表查询计数器 - 先查索引表再查分表的查询方式 */
    private final Counter indexThenScanCounter;

    /** 受限扫描查询计数器 - 需要全表扫描但有限制的查询方式 */
    private final Counter limitedScanCounter;

    /** 禁止查询计数器 - 被拒绝执行的查询统计 */
    private final Counter forbiddenQueryCounter;

    /**
     * 初始化监控指标
     * 在Spring容器启动后执行，注册各种监控计数器
     */
    @PostConstruct
    public void init() {
        // 注册各种查询策略的监控指标
        directShardingCounter = meterRegistry.counter("query.direct.sharding");
        indexThenScanCounter = meterRegistry.counter("query.index.then.scan");
        limitedScanCounter = meterRegistry.counter("query.limited.scan");
        forbiddenQueryCounter = meterRegistry.counter("query.forbidden");
    }

    /**
     * 记录查询策略使用情况
     * @param strategy 使用的查询策略
     */
    public void recordQueryStrategy(QueryStrategy strategy) {
        switch (strategy) {
            case DIRECT_SHARDING:
                // 最优查询：直接通过分片键路由到具体分表
                directShardingCounter.increment();
                break;
            case INDEX_THEN_SCAN:
                // 索引表查询：先查索引表获取分片键，再查分表
                indexThenScanCounter.increment();
                break;
            case LIMITED_SCAN:
            case SCROLL_SCAN:
            case AGGREGATION_SCAN:
                // 受限查询：需要全表扫描但有各种限制措施
                limitedScanCounter.increment();
                break;
            case FORBIDDEN:
                // 禁止查询：会导致严重性能问题的查询类型
                forbiddenQueryCounter.increment();
                break;
        }
    }

    /**
     * 记录查询警告信息
     * @param message 警告消息
     * @param context 查询上下文
     */
    public void recordWarning(String message, QueryContext context) {
        // 记录详细的警告日志
        log.warn("Query Warning: {} - SQL: {}", message, context.getSql());
        // 发送到监控系统，触发相应的告警机制
        alertManager.sendWarning(message, context);
    }
}
```
```

**降级查询监控：**

```java
/**
 * 降级查询监控组件
 * 专门监控那些需要全表扫描的降级查询，确保系统性能可控
 */
@Component
public class DegradedQueryMonitor {

    /** 降级查询次数计数器 */
    private Counter degradedQueryCounter;

    /** 降级查询执行时间计时器 */
    private Timer degradedQueryTimer;

    /**
     * 初始化降级查询监控指标
     */
    @PostConstruct
    public void init() {
        // 统计降级查询的执行次数
        degradedQueryCounter = meterRegistry.counter("query.degraded.count");
        // 统计降级查询的执行时间分布
        degradedQueryTimer = meterRegistry.timer("query.degraded.time");
    }

    /**
     * 记录降级查询的执行情况
     * @param queryType 查询类型（如LIST、PAGE、AGGREGATION等）
     * @param executionTime 查询执行时间（毫秒）
     */
    public void recordDegradedQuery(String queryType, long executionTime) {
        // 增加降级查询计数
        degradedQueryCounter.increment();

        // 记录查询执行时间，用于性能分析
        degradedQueryTimer.record(executionTime, TimeUnit.MILLISECONDS);

        // 如果执行时间超过告警阈值，触发慢查询告警
        if (executionTime > alertThresholdMs) {
            alertSlowDegradedQuery(queryType, executionTime);
        }
    }

    /**
     * 发送慢查询告警
     * @param queryType 查询类型
     * @param executionTime 执行时间
     */
    private void alertSlowDegradedQuery(String queryType, long executionTime) {
        String message = String.format("慢降级查询告警: 类型=%s, 执行时间=%dms", queryType, executionTime);
        log.error(message);
        // 发送到告警系统
        alertManager.sendAlert("SLOW_DEGRADED_QUERY", message);
    }
}
```

### 3.5.4 不支持场景的替代方案

对于索引表不适合支持的查询场景，我们提供以下替代方案：

1. **数据仓库查询**：复杂的历史数据分析和报表查询应通过数据仓库系统实现
2. **离线批处理**：大范围的数据导出和统计应使用离线批处理作业
3. **专门的查询服务**：为运营和客服等特殊场景提供独立的查询服务，可以有更宽松的资源限制

通过这套热数据索引表方案，我们在保持系统简洁性的同时，有效解决了分表后非分片键查询的性能问题，为绝大多数业务场景提供了高效的查询能力，同时为特殊场景提供了明确的替代方案。
