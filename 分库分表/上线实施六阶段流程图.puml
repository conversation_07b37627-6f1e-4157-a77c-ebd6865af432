@startuml 上线实施六阶段流程图

!theme plain
skinparam backgroundColor #FFFFFF
skinparam defaultFontName "Microsoft YaHei"
skinparam defaultFontSize 12

' 美化样式设置
skinparam activity {
    BackgroundColor #F8F9FA
    BorderColor #6C757D
    FontColor #212529
    FontSize 11
}

skinparam note {
    BackgroundColor #FFF3CD
    BorderColor #FFEAA7
    FontSize 10
}

skinparam partition {
    BorderThickness 2
    FontStyle bold
    FontSize 13
}

title 分库分表上线实施流程图

partition "🔧 **阶段一：环境准备**" #E3F2FD {
    :代码与表结构就绪;
    note right: AOP代码、分片表、索引表

    :初始配置设置;
    note right: dual-write=false, read.path.mode=sharding
}

partition "📦 **阶段二：存量迁移**" #E8F5E8 {
    :启动数据迁移;
    note right: 批量迁移历史数据

    :实时校验数据;
    note right: 迁移完成批次立即校验

    :迁移完成确认;
}

partition "✏️ **阶段三：增量双写**" #FFF3E0 {
    :开启双写模式;
    note right: dual-write=true

    :双写稳定运行;
    note right: 监控写入性能与冲突率
}

partition "🔍 **阶段四：数据校验**" #F3E5F5 {
    :全量数据校验;
    note right: 新旧表数据完全一致性检查

    :修复不一致数据;
    note right: 处理冲突并重新校验
}

partition "🚀 **阶段五：灰度发布**" #FCE4EC {
    :启动灰度切换;
    note right: read.path.mode=grayscale_migration

    :渐进式流量切换;
    note right: 0% → 25% → 50% → 75% → 100%

    :全量流量验证;
}

partition "🎯 **阶段六：架构固化**" #F5F5F5 {
    :系统稳定观察;
    note right: 观察期确保系统稳定

    :配置最终固化;
    note right: dual-write=false, read.path.mode=sharding

    :项目收尾归档;
}

@enduml
