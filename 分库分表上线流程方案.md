# 分库分表上线流程方案

## 概述

本文档基于《分库分表技术方案 FIN.md》，详细描述分库分表系统的上线流程。整个上线过程分为四个阶段：准备工作、发布上线与数据迁移、灰度放量、全量上线。每个阶段都有明确的完成指标和回滚策略，确保系统平稳过渡。

---

## 第一阶段：准备工作（上线前）

### 1.1 分表创建

**目标：** 完成所有物理分表的创建，为数据迁移做好基础设施准备。

**执行内容：**

1. **分表结构设计确认**
   - 确认分表数量：每个分库内创建128张分表
   - 确认分表命名规则：`{原表名}_{0..127}`
   - 确认分表字段结构与原表完全一致

2. **分表创建脚本执行**
   ```sql
   -- 示例：创建订单分表
   CREATE TABLE `t_trans_order_0` LIKE `t_trans_order`;
   CREATE TABLE `t_trans_order_1` LIKE `t_trans_order`;
   ...
   CREATE TABLE `t_trans_order_127` LIKE `t_trans_order`;
   ```

3. **索引创建**
   - 为每张分表创建与原表相同的索引
   - 特别关注分片键相关的索引优化

4. **权限配置**
   - 为应用账号配置新分表的读写权限
   - 确认数据库连接池配置能够支持分表访问

**完成标准：**
- [ ] 所有分表创建完成，结构与原表一致
- [ ] 索引创建完成，性能测试通过
- [ ] 权限配置完成，连接测试通过

### 1.2 迁移脚本开发

**目标：** 开发高可用的存量数据迁移工具，支持断点续传和并发处理。

**核心组件开发：**

1. **迁移引擎核心**
   - 基于三优级决策的并发执行模型
   - 任务窗口四态生命周期管理（PROCESSING/WAITING/FAILED/SUCCEEDED）
   - Worker的救援队-维修工-接力者-开拓者决策流程

2. **数据源适配器**
   - 支持巨型单表直接迁移
   - 支持按月归档全表聚合迁移
   - 支持按月归档大字段聚合迁移

3. **分片路由算法**
   - 实现哈希取模分片算法（128分片）
   - 确保数据均匀分布到各个分表

4. **断点续传机制**
   - 基于checkpoint_id的批次处理
   - 任务状态持久化存储
   - 故障自动恢复能力

**完成标准：**
- [ ] 迁移脚本开发完成，单元测试通过
- [ ] 性能测试达标（处理速度、资源消耗）
- [ ] 断点续传功能验证通过
- [ ] 并发安全性测试通过

### 1.3 校验脚本开发

**目标：** 开发数据一致性校验工具，确保迁移数据的完整性和正确性。

**校验脚本功能：**

1. **迁移校验一体化工具**
   - "删-插-查-比"(Delete-Insert-Select-Compare)工作模式
   - 数量校验：确保源表和目标表记录数一致
   - 内容校验：逐字段比对数据内容
   - 冲突记录：将不一致数据记录到冲突表

2. **并行校验机制**
   - 在迁移N月数据时，同时校验N-1月数据
   - 确保已迁移数据的持续一致性

3. **冲突处理机制**
   - 冲突表设计：记录所有数据不一致情况
   - 冲突分类：区分双写冲突和迁移竞态冲突
   - 修复策略：以旧表数据为准，覆盖新表数据

**完成标准：**
- [ ] 校验脚本开发完成，功能测试通过
- [ ] 冲突检测机制验证通过
- [ ] 修复脚本开发完成，幂等性测试通过

### 1.4 数据源切换逻辑开发

**目标：** 开发基于AOP的分库分表调度引擎，实现业务代码零侵入。

**核心组件开发：**

1. **ShardingSphere-JDBC配置**
   - 编程式配置多数据源
   - 统一配置源管理（Nacos + APS）
   - 分片规则配置（第一阶段只配置分库规则）

2. **AOP调度引擎**
   - `ShardingIndexAspect`：AOP切面调度器
   - `ShardingExecutionEngine`：执行引擎和事务边界管理
   - `WriteOperationHandler`：写操作处理器
   - `ReadOperationHandler`：读操作处理器

3. **策略模式实现**
   - `StandardWriteStrategy`：标准写策略（最终稳态）
   - `DualWriteTransitionalStrategy`：双写过渡策略
   - `ShardingReadStrategy`：分片读策略（最终稳态）
   - `GrayscaleReadTransitionalStrategy`：灰度读过渡策略

4. **热数据索引表**
   - 索引表结构设计
   - 生命周期管理（数据保留策略）
   - 定时清理脚本

**完成标准：**
- [ ] AOP调度引擎开发完成，单元测试通过
- [ ] 策略切换机制验证通过
- [ ] 热数据索引表功能测试通过
- [ ] 事务一致性测试通过

---

## 第二阶段：发布上线与数据迁移（数据追平）

### 2.1 开启存量数据迁移脚本

**目标：** 启动存量数据迁移，将历史数据从旧表迁移到新分表。

**执行步骤：**

1. **迁移任务启动**
   - 部署迁移应用到独立服务器
   - 配置迁移参数（批次大小、并发数、限流设置）
   - 启动迁移任务，开始处理存量数据

2. **迁移进度监控**
   - 实时监控迁移进度和性能指标
   - 监控数据库资源使用情况
   - 记录迁移日志和异常情况

3. **迁移质量控制**
   - 定期执行数据校验
   - 监控冲突记录数量
   - 确保迁移过程不影响线上业务

**完成标准：**
- [ ] 迁移脚本正常运行，无阻塞性错误
- [ ] 数据库性能指标正常，未影响线上业务
- [ ] 迁移进度符合预期时间计划

### 2.2 开启双写，同步写老表和新表

**目标：** 启动双写机制，确保新产生的数据同时写入新旧两套表。

**执行步骤：**

1. **配置切换**
   ```properties
   # 启用双写过渡策略
   sharding-architecture.dual-write=Y
   ```

2. **双写逻辑验证**
   - 验证INSERT操作同时写入新旧表
   - 验证UPDATE操作的冲突处理机制
   - 验证事务一致性保障

3. **冲突监控**
   - 监控双写冲突记录表
   - 分析冲突类型和频率
   - 确保冲突处理机制正常工作

**完成标准：**
- [ ] 双写机制正常启动，无功能异常
- [ ] 新增数据同时存在于新旧表中
- [ ] 冲突检测和记录机制正常工作

### 2.3 写老表，读老表

**目标：** 保持读写都在旧表，确保业务稳定性。

**执行步骤：**

1. **读策略确认**
   ```properties
   # 确保读取仍然从旧表
   sharding-architecture.read.path.mode=grayscale_migration
   sharding-architecture.read.grayscale.percentage=0
   ```

2. **业务功能验证**
   - 执行完整的业务功能测试
   - 确认所有读写操作正常
   - 验证性能指标符合预期

**完成标准：**
- [ ] 所有业务功能正常，性能稳定
- [ ] 读写操作均在旧表执行
- [ ] 双写机制在后台正常运行

### 2.4 完成指标与回滚策略

**完成指标：**
- [ ] 存量数据迁移完成度达到95%以上
- [ ] 双写机制稳定运行，冲突率低于1%
- [ ] 业务功能完全正常，性能无明显下降
- [ ] 数据一致性校验通过率达到99%以上

**回滚策略：**
- **无需回滚**：本阶段只是数据准备，不影响业务逻辑
- **数据验证**：重点进行数据完整性和一致性验证
- **问题处理**：发现问题及时修复，确保数据质量

---

## 第三阶段：灰度（放量）

### 3.1 保持双写

**目标：** 继续保持双写机制，为读切换提供数据保障。

**执行内容：**
- 维持双写配置不变
- 持续监控双写性能和冲突情况
- 确保新表数据的实时性和完整性

### 3.2 读新表灰度放量（1-100%）

**目标：** 逐步将读流量从旧表切换到新表，验证新架构的稳定性。

**执行步骤：**

1. **1%灰度开始**
   ```properties
   sharding-architecture.read.path.mode=grayscale_migration
   sharding-architecture.read.grayscale.percentage=1
   ```

2. **逐步放量**
   - 1% → 5% → 10% → 25% → 50% → 75% → 100%
   - 每个阶段观察2-4小时，确认稳定后进入下一阶段
   - 重点监控性能指标、错误率、响应时间

3. **性能对比分析**
   - 对比新旧表的查询性能
   - 分析热数据索引表的命中率
   - 监控数据库资源使用情况

### 3.3 数据验证与生产观察

**数据验证：**
- 实时对比新旧表查询结果
- 验证热数据索引表的准确性
- 检查分片路由的正确性

**生产观察：**
- 监控业务指标：成功率、响应时间、吞吐量
- 监控系统指标：CPU、内存、数据库连接数
- 监控错误日志和异常情况

### 3.4 完成指标与回滚策略

**完成指标：**
- [ ] 100%读流量切换到新表
- [ ] 数据验证通过率达到99.9%以上
- [ ] 业务指标稳定，性能提升明显
- [ ] 系统运行稳定，无重大异常
- [ ] 在100%流量下稳定运行72小时以上

**回滚策略：**
- **快速回滚**：调整配置立即切回读旧表
  ```properties
  sharding-architecture.read.grayscale.percentage=0
  ```
- **回滚条件**：
  - 错误率超过阈值（如0.1%）
  - 响应时间显著增加（如超过原来的150%）
  - 出现数据不一致问题
  - 系统资源异常（如数据库连接池耗尽）

---

## 第四阶段：全量上线

### 4.1 写老，读老 → 调整读写策略配置

**目标：** 完成最终的策略切换，进入系统稳态。

**执行步骤：**

1. **切换到标准策略**
   ```properties
   # 关闭双写，使用标准写策略
   sharding-architecture.dual-write=N

   # 使用分片读策略
   sharding-architecture.read.path.mode=sharding
   ```

2. **验证策略切换**
   - 确认写操作只写新表（分表+索引表）
   - 确认读操作直接从新表读取
   - 验证所有业务功能正常

3. **性能优化**
   - 调整数据库连接池配置
   - 优化分片查询性能
   - 调整热数据索引表保留策略

### 4.2 数据归档

**目标：** 处理旧表数据，完成系统清理。

**执行步骤：**

1. **数据归档准备**
   - 确认新表数据完整性
   - 制定旧表数据归档计划
   - 准备数据备份和恢复方案

2. **旧表数据处理**
   - 将旧表数据归档到历史库
   - 保留必要的历史数据查询接口
   - 清理不再使用的旧表结构

3. **系统清理**
   - 移除双写相关代码和配置
   - 清理迁移过程中的临时表和日志
   - 更新系统文档和运维手册

**完成标准：**
- [ ] 新架构稳定运行，性能达到预期
- [ ] 旧表数据安全归档，可追溯查询
- [ ] 系统代码和配置清理完成
- [ ] 运维文档更新完成

---

## 风险控制与应急预案

### 关键风险点

1. **数据一致性风险**
   - 双写过程中的数据不一致
   - 迁移过程中的竞态条件
   - 灰度切换过程中的数据差异

2. **性能风险**
   - 双写导致的性能下降
   - 分表查询的性能问题
   - 数据库资源消耗增加

3. **业务连续性风险**
   - 切换过程中的服务中断
   - 异常情况下的快速回滚
   - 关键业务功能的影响

### 应急预案

1. **数据不一致应急处理**
   - 立即停止灰度放量
   - 启动数据修复脚本
   - 必要时回滚到旧表读取

2. **性能问题应急处理**
   - 调整数据库连接池配置
   - 优化慢查询和索引
   - 必要时降低灰度比例

3. **业务中断应急处理**
   - 立即回滚到上一个稳定状态
   - 启动业务降级方案
   - 通知相关业务方和用户

### 配置开关管理

所有策略切换通过配置中心动态控制，支持实时调整：

```properties
# 双写控制开关
sharding-architecture.dual-write=Y/N

# 读策略控制
sharding-architecture.read.path.mode=sharding/grayscale_migration

# 灰度比例控制
sharding-architecture.read.grayscale.percentage=0-100
```

### 监控告警体系

每个阶段都有对应的监控指标和告警机制：

1. **数据库性能监控**
   - 连接池使用率
   - SQL执行时间
   - 慢查询统计

2. **业务接口监控**
   - 接口响应时间
   - 成功率统计
   - 错误率告警

3. **数据一致性监控**
   - 双写冲突数量
   - 数据校验结果
   - 迁移进度跟踪

4. **系统资源监控**
   - CPU和内存使用率
   - 磁盘I/O性能
   - 网络连接状态

---

## 总结

本上线流程方案通过四个阶段的渐进式推进，确保分库分表系统的平稳上线：

1. **准备工作阶段**：完成所有技术准备，确保基础设施就绪
2. **数据迁移阶段**：启动双写和存量迁移，实现数据追平
3. **灰度放量阶段**：逐步切换读流量，验证新架构稳定性
4. **全量上线阶段**：完成最终切换，进入系统稳态

每个阶段都有明确的完成指标和回滚策略，最大程度降低上线风险，确保业务连续性和数据安全性。整个流程设计遵循"小步快跑、快速验证、及时回滚"的原则，为分库分表的复杂场景提供了完整的解决方案。
