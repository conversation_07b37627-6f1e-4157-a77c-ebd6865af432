---

# 项目背景与核心挑战
## 现有架构：成熟的分库体系
目前系统已成功实施了基于**融担号**的数据库水平分片（分库）架构。该架构通过AOP切面拦截DAO层调用，从业务上下文中提取分库键，并利用动态数据源（`DynamicDataSource`）机制，将数据库请求精准路由至对应的物理分库。这套分库体系运行稳定，有效分散了整体数据压力。

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753780821944-49e5d611-1410-4d11-a837-29f5bdf7cfcb.svg)

## 新的瓶颈：库内单表膨胀
尽管分库机制有效，但随着业务的持续高速增长，新的性能瓶颈在**分库内部**浮现。部分融担数据高度集中，导致其所在的单个物理分库中，核心业务表（如 `t_trans_order`）的数据量再次达到性能极限，单表数据规模已达数亿级别。

这种库内单表的过度膨胀，已引发以下核心痛点：

+ **查询性能急剧下降：** 对大表的查询，尤其是非主键查询和分页查询，响应时间显著增长，严重影响用户体验。
+ **数据库维护困难：** 对大表的DDL操作（如加索引、改字段）耗时漫长，锁表风险高，数据库的日常维护和迭代变得异常困难。
+ **潜在的稳定性风险：** 大规模的慢查询持续消耗数据库资源，对整个分库的稳定性构成严重威胁。

## 核心目标：引入库内分表机制
为彻底解决上述问题，本方案的核心目标是：在现有成熟、稳定的分库体系之上，引入**库内分表**（Table Sharding）机制。通过将单库内的大表进一步水平拆分为多张物理子表，将单点的数据压力和查询负载均匀分散，从而恢复并保障系统的长效高性能与高可用性。

---







# 第一阶段：ShardingSphere-JDBC的平滑迁移
## 迁移的必要性
### 分表需求驱动的技术选型
随着业务数据的持续增长，现有的分库架构已无法满足单库内大表的性能要求。为了实现库内分表能力，我们面临一个关键的技术选型问题：

**核心冲突：** 现有的`DynamicDataSource`多数据源管理方案与`ShardingSphere-JDBC`分片中间件存在根本性的架构冲突，两者无法同时使用。

+ **DynamicDataSource的局限性：** 虽然在分库场景下运行稳定，但其设计理念是"多个独立数据源的切换"，无法支持ShardingSphere所需的"逻辑表到物理表的映射"能力。
+ **ShardingSphere的要求：** 作为分片中间件，需要完全接管数据源管理，通过逻辑SQL到物理SQL的转换实现分库分表，与DynamicDataSource的数据源切换机制冲突。

**技术决策：** 为了获得分表能力，必须将现有的DynamicDataSource方案完全替换为ShardingSphere-JDBC，这是一个不可避免的架构升级。

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753779545346-352d162e-c675-41db-a0ef-fb8ffd961ba4.svg)

### 现有自研方案的局限性
除了分表能力的硬性要求外，现有自研的动态数据源方案还存在以下问题：

+ **扩展性不足：** 面对复杂的分片场景（如分库分表、读写分离等），自研方案需要大量定制开发，维护成本高。
+ **与主流技术栈脱节：** 团队需要维护大量自研代码，新成员学习成本高，技术债务持续累积。
+ **功能局限性：** 缺乏分布式事务、SQL解析优化、分片算法等企业级特性。

**解决方案：** 采用业界成熟的ShardingSphere-JDBC，获得标准化的分片能力和丰富的企业级特性。

## ShardingSphere-JDBC编程式配置
### 多配置源的统一管理
现有系统的数据源配置来自两个不同的源头，迁移方案必须保持这种配置模式的兼容性：

**配置源现状：**

+ **Nacos配置中心：** 存储部分融担的数据库连接信息
+ **APS Dubbo服务：** 通过远程调用获取另一部分融担的配置信息

**统一抽象元数据获取接口与整合分片接口：**

```java
public interface ConfigurationSource {
    /**
     * 获取数据源配置信息
     * @return 数据源配置映射
     */
    Map<String, DataSourceConfiguration> getDataSourceConfigs();

    /**
     * 获取分片规则配置
     * @return 分片规则配置
     */
    ShardingRuleConfiguration getShardingRuleConfig();
}
```

**Nacos+APS实现接口获取不同来源的配置：**

```java
@Component
public class NacosConfigurationSource implements ConfigurationSource {

    @Autowired
    private ConfigService configService;

    @Override
    public Map<String, DataSourceConfiguration> getDataSourceConfigs() {
        //TODO
    }
}

@Component
public class ApsConfigurationSource implements ConfigurationSource {

    @Reference
    private ApsConfigService apsConfigService;

    @Override
    public Map<String, DataSourceConfiguration> getDataSourceConfigs() {
        //TODO
    }
}
```

**多来源数据配置合并策略：**

```java
@Component
public class CompositeConfigurationSource implements ConfigurationSource {

    @Autowired
    private List<ConfigurationSource> configSources;

    @Override
    public Map<String, DataSourceConfiguration> getDataSourceConfigs() {
        Map<String, DataSourceConfiguration> mergedConfig = new HashMap<>();

        // 按优先级合并配置：APS > Nacos
        for (ConfigurationSource source : configSources) {
            Map<String, DataSourceConfiguration> configs = source.getDataSourceConfigs();
            mergedConfig.putAll(configs);
        }

        return mergedConfig;
    }
}
```

### 动态数据源构建
在应用启动时，通过编程方式动态构建ShardingSphere数据源：

```java
@Configuration
public class ShardingSphereDataSourceConfig {

    @Autowired
    private CompositeConfigurationSource configSource;

    @Bean
    @Primary
    public DataSource shardingSphereDataSource() {
        try {
            // 1. 获取所有数据源配置
            Map<String, DataSourceConfiguration> dataSourceConfigs = configSource.getDataSourceConfigs();

            // 2. 创建物理数据源
            Map<String, DataSource> dataSourceMap = createDataSources(dataSourceConfigs);

            // 3. 构建分片规则（第一阶段只包含分库规则）
            ShardingRuleConfiguration shardingRuleConfig = buildShardingRules();

            // 4. 创建ShardingSphere数据源
            return ShardingSphereDataSourceFactory.createDataSource(
                dataSourceMap, 
                Collections.singleton(shardingRuleConfig), 
                new Properties()
            );

        } catch (Exception e) {
            throw new DataSourceInitializationException("Failed to create ShardingSphere DataSource", e);
        }
    }

    private Map<String, DataSource> createDataSources(Map<String, DataSourceConfiguration> configs) {
        Map<String, DataSource> dataSourceMap = new HashMap<>();

        for (Map.Entry<String, DataSourceConfiguration> entry : configs.entrySet()) {
            DataSourceConfiguration config = entry.getValue();
            HikariDataSource dataSource = new HikariDataSource();

            dataSource.setJdbcUrl(config.getUrl());
            dataSource.setUsername(config.getUsername());
            dataSource.setPassword(config.getPassword());
            dataSource.setDriverClassName(config.getDriverClassName());

            // 设置连接池参数
            dataSource.setMaximumPoolSize(config.getMaxPoolSize());
            dataSource.setMinimumIdle(config.getMinIdle());
            dataSource.setConnectionTimeout(config.getConnectionTimeout());

            dataSourceMap.put(entry.getKey(), dataSource);
        }

        return dataSourceMap;
    }

    private ShardingRuleConfiguration buildShardingRules() {
        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();

        // 第一阶段：只配置分库规则，不配置分表规则
        ShardingTableRuleConfiguration orderTableRule = new ShardingTableRuleConfiguration(
            "t_order", 
            "db_${0..3}.t_order"  // 只分库，不分表
        );

        // 配置分库策略
        orderTableRule.setDatabaseShardingStrategy(
            new StandardShardingStrategyConfiguration("guarantee_no", "guaranteeNoShardingAlgorithm")
        );

        shardingRuleConfig.getTables().add(orderTableRule);

        // 注册分片算法
        shardingRuleConfig.getShardingAlgorithms().put(
            "guaranteeNoShardingAlgorithm", 
            new AlgorithmConfiguration("HINT", new Properties())
        );

        return shardingRuleConfig;
    }
}
```

## 统一AOP路由机制设计
### 现有上下文路由的保持
现有系统通过AOP切面从ServiceContext获取融担号进行数据源路由，迁移后必须保持这种机制不变：

**现状保持：**

+ SQL语句中不包含融担号条件
+ 通过ServiceContext.getGuaranteeNo()获取路由信息
+ 业务代码无需任何修改

### 统一切面的架构设计
设计一个统一的AOP切面，同时处理分库和分表路由（第一阶段只启用分库）

### 自定义Hint分片算法
实现基于融担号的分库算法(兼容现在的逻辑)：

```java
public class GuaranteeNoHintShardingAlgorithm implements HintShardingAlgorithm<String> {

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, 
                                       HintShardingValue<String> shardingValue) {

        String guaranteeNo = getGuaranteeNoFromHint(shardingValue);

        //TODO
    }
}
```

## 第一阶段实现策略
### 只做分库，不做分表
第一阶段的核心原则是**功能对等**，确保迁移后的分库能力与现有方案完全一致：

**配置约束：**

+ ShardingSphere配置中只包含分库规则
+ 所有表的actualDataNodes配置为：`db_${0..3}.table_name`（不包含分表后缀）
+ 分表相关的算法和策略暂不配置

**代码约束：**

+ 统一AOP中分表逻辑预留但不启用
+ `isTableShardingEnabled()`方法固定返回false
+ `extractTableShardingKey()`方法返回null

### 功能对等性验证
建立完整的验证机制确保迁移成功：

```java
@Component
public class MigrationValidator {

    public void validateRouting() {
        // 1. 路由一致性测试
        validateDatabaseRouting();

        // 2. 性能对比测试
        validatePerformance();

        // 3. 事务一致性测试
        validateTransactionConsistency();
    }
}
```

## 为分表阶段奠定基础
### 架构统一的收益
完成第一阶段迁移后，系统将获得以下收益：

+ **技术栈标准化：** 采用业界主流的ShardingSphere-JDBC，降低维护成本
+ **分表能力准备：** 为第二阶段的分表改造提供了技术基础
+ **功能增强：** 获得SQL解析优化、分布式事务等企业级特性

### 平滑过渡的保障
+ **业务逻辑零侵入：** 所有业务代码保持不变，SQL语句无需修改
+ **运维复杂度可控：** 配置管理方式保持一致，运维流程无需调整
+ **风险隔离：** 第一阶段只做技术栈替换，不涉及业务逻辑变更

通过这种分阶段的迁移策略，我们在保证系统稳定性的前提下，为后续的分表改造奠定了坚实的技术基础。第一阶段完成后，系统将具备完整的ShardingSphere分片能力，为第二阶段的库内分表改造做好了充分准备。

# 第二阶段：库内分表与热数据索引构建
## 分表的必要性
随着业务的持续高速增长，尽管现有的分库机制有效分散了整体数据压力，但新的性能瓶颈在**分库内部**浮现。部分融担数据高度集中，导致其所在的单个物理分库中，核心业务表（如 `t_trans_order`）的数据量再次达到性能极限，单表数据规模已达数亿级别。

这种库内单表的过度膨胀，已引发以下核心痛点：

+ **查询性能急剧下降：** 对大表的查询，尤其是非主键查询和分页查询，响应时间显著增长，严重影响用户体验。
+ **数据库维护困难：** 对大表的DDL操作（如加索引、改字段）耗时漫长，锁表风险高，数据库的日常维护和迭代变得异常困难。
+ **潜在的稳定性风险：** 大规模的慢查询持续消耗数据库资源，对整个分库的稳定性构成严重威胁。

**解决方案：** 在现有成熟、稳定的分库体系之上，引入**库内分表**（Table Sharding）机制。通过将单库内的大表进一步水平拆分为多张物理子表，将单点的数据压力和查询负载均匀分散。

## 分表策略设计
### 分片键选择
+ **分库键:** 融担号维持不变，以确保对现有分库逻辑的兼容性。
+ **库内分表键:** 
    - **优先使用：** 服务级统一分片键探索。在单个服务内部，寻找一个"黄金业务分表键"。这个分表键可以作为该服务内所有核心表的统一分片标准，这样所有的核心查询和修改都可以通过分表键精准路由物理表。然而，此类普适的"黄金分片键"通常难以找到。
    - **降级到单表：** 若确认不存在则降级为单表级别。鉴于存量业务的复杂性，基于不改业务的逻辑，将分表键的决策粒度被细化至每一张独立的数据表。对于每张待分表的表，以及对于业务的理解，需要考虑的问题是："对于这张表，用哪个字段做分片键，能让最多的核心查询语句最高效？"

通过此层决策，项目内绝大多数的表都将拥有一个最**适合自身业务场景的分片键**。

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753784207848-5042ffe1-73bf-4dad-9db0-3e114d307b6e.svg)

### 分表数量规划
+ **分表数量：** 综合考虑未来2-3年的业务增长、单表容量以及DDL广播效率，确定单库内分表数量为 **128** 张。
+ **单表数据量压力评估**
    - **5亿存量 +日增300万**
        * **两年后单库总数据量:** `300万/天 * 720天 ≈ 21亿`
        * **单表承载数据量:** `21亿 / 128张表 ≈` **1687.5万**
        * **结论:** 单表数据量低于2000万的理想值，此风险被认为是可接受的。
    - **10亿存量 + 日增 1000万**
        * **两年后单库总数据量:** `1000万/天 * 720天 ≈ 72亿`
        * **单表承载数据量:** `72亿 / 128张表 ≈` **5625万**
        * **结论:** 单表数据量高于2000万的理想值，不增加分表的前提下, 需要减少存储时长, 不过此类表较少, 目前支付模块仅trans_order表在8亿存量+900w增量左右, 其他系统未做调研。

### 分片算法实现
采用哈希取模算法确保数据均匀分布：

```java
/**
 * 哈希取模分片算法实现
 * 用于将数据根据分片键均匀分布到128张分表中
 */
public class HashModShardingAlgorithm implements StandardShardingAlgorithm<String> {

    /**
     * 分表数量：128张
     * 基于2-3年业务增长预估，单表数据量控制在2000万以内
     */
    private static final int SHARD_COUNT = 128;

    /**
     * 执行分片路由逻辑
     * @param availableTargetNames 可用的目标表名集合
     * @param shardingValue 分片键值对象
     * @return 目标分表名称
     */
    @Override
    public String doSharding(Collection<String> availableTargetNames,
                           ShardingValue<String> shardingValue) {
        // 获取分片键的值（如订单ID、用户ID等）
        String value = shardingValue.getValue();

        // 计算哈希值，确保相同的分片键总是路由到同一张表
        int hashCode = value.hashCode();

        // 取绝对值并对分表数量取模，得到分表索引（0-127）
        int shardIndex = Math.abs(hashCode) % SHARD_COUNT;

        // 根据索引找到对应的物理表名
        return findTargetByIndex(availableTargetNames, shardIndex);
    }
}
```

## 非分表键查询的性能挑战
分表后，所有不带分片键的查询将无法路由到具体的物理表，面临以下问题：

+ **全表扫描风险：** ShardingSphere在无分片键时，默认会对所有分片执行广播查询，在128个分片上并行执行，瞬间对数据库造成巨大I/O和连接池压力。
+ **性能急剧下降：** 原本的单表查询变成了128个表的并行查询，响应时间和资源消耗成倍增长。
+ **系统稳定性威胁：** 大量的广播查询可能引发数据库雪崩，严重威胁系统稳定性。

**传统解决方案的局限性：**

+ 外部搜索引擎（如ES）：增加系统复杂度，存在数据一致性问题
+ 冗余字段：业务侵入性强，维护成本高
+ 中间件路由表：需要额外的存储和维护成本

## 热数据索引表解决方案
### 热数据模型 
基于业务访问模式的分析：**最近产生的数据具有最高的访问频率**。例如：

+ 绝大部分的查询集中在最近的一段时间数据
+ 业务主要关注最新的交易记录和状态
+ 历史数据的访问频率随时间呈指数级下降

基于这个**热数据模型**，我们设计了一个轻量级的索引表方案。

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753783000261-62d6c907-de09-48ba-a311-721d45568755.svg)

### 索引表设计
我们创建一张普通的单表 `t_trans_order_index`，专门存储**非分片键到分片键的映射关系**：

```sql
-- 热数据索引表设计
-- 目的：存储非分片键到分片键的映射关系，解决分表后的查询路由问题
CREATE TABLE `t_trans_order_index` (
  -- 主键(可能存在,分表键采用主键的情况)
  `id` VARCHAR(64) NOT NULL COMMENT '主键ID，与业务表ID一致',

  -- 分表键
  `cust_no` VARCHAR(32) NOT NULL COMMENT '客户号',

  -- 业务查询字段：存储常用的非分片键查询条件
  `loan_order_id` VARCHAR(32) NOT NULL COMMENT '借据号',
  -- 多个查询字段, ...

  

  -- 时间字段：用于热数据管理和数据清理
  `create_datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 主键索引
  PRIMARY KEY (`id`),

  -- 业务查询索引：支持各种非分片键查询
  INDEX `idx_cust_no` (`cust_no`),
  INDEX `idx_loan_order_id` (`loan_order_id`),
  INDEX `idx_create_time` (`create_time`) -- 时间范围查询和数据清理
) ENGINE=InnoDB COMMENT='t_trans_order表热数据索引表';
```

**设计要点：**

+ **普通单表结构**：无分区，结构简单，维护方便
+ **映射关系存储**：存储业务查询中常用的非分片键字段
+ **主键就是分片键**：通过ID可以直接路由到具体分表
+ **精简字段**：只存储查询必需的字段，保持表结构轻量

## 索引表的生命周期管理
### 数据保留策略与热度统计
+ **动态配置的时间窗口：** 索引表只保留最近一段时间的热数据，通过配置中心动态设置保留时间。
+ **热度统计机制：**
    - 可选开启的查询热度统计功能
    - 记录不同时间范围内的查询频率分布
    - 为时间窗口优化提供数据支撑
    - 通过配置控制是否启用，避免对性能产生影响

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753784929453-0d71159c-16f8-4fd4-82d7-c5f9233d493d.svg)

+ **配置化管理：** 通过配置中心统一管理数据保留时间，支持动态调整。

```properties
# 索引表数据保留配置
# 热数据保留天数：只保留最近14天的数据，超过的自动清理
index.table.retention.days=14

# 数据清理调度：每天凌晨2点执行清理任务
index.table.cleanup.schedule=0 2 * * *

# 查询热度统计开关：是否启用查询热度统计功能
index.table.stats.enabled=true

# 统计采样率：1%的查询会被统计，避免对性能产生影响
index.table.stats.sample.rate=0.01
```

**热度统计实现(可以在后面的查询策略中增加一个统计策略用于找到一个业务最佳的索引保留时间范围)：**

```java
/**
 * 查询热度统计收集器
 * 用于统计不同时间范围内的查询频率分布，为索引表时间窗口优化提供数据支撑
 */
@Component
public class QueryHeatStatisticsCollector {

    /**
     * 记录查询操作，用于热度统计
     * @param queryTime 查询的数据时间（不是查询发起时间）
     */
    public void recordQuery(Date queryTime) {
        // 如果统计功能未开启或不在采样范围内，直接返回
        if (!statsEnabled || !shouldSample()) {
            return;
        }

        // 计算查询数据距离当前时间的天数
        int daysAgo = calculateDaysFromNow(queryTime);
        // 增加对应天数的查询计数
        incrementQueryCount(daysAgo);
    }
}
```

### 定时清理脚本
设计自动化的数据清理机制：

```sql
-- 索引表数据清理脚本
-- 删除超过保留期的热数据，释放存储空间
-- 使用LIMIT控制每次删除的数量，避免长时间锁表
DELETE FROM t_trans_order_index
WHERE create_time < DATE_SUB(NOW(), INTERVAL ${retention_days} DAY)
LIMIT 10000;  -- 每次最多删除1万条记录，分批执行
```

**清理策略：**

+ **分批删除：** 每次删除固定数量的记录，避免长时间锁表
+ **定时执行：** 在业务低峰期执行，减少对线上业务的影响
+ **监控告警：** 监控清理脚本的执行状态，确保数据生命周期管理正常

### 查询场景与降级策略
#### 热数据索引表的固有局限性
热数据索引表虽然有效解决了分表后的查询路由问题，但其设计也带来了一些固有的局限性：

+ **时间窗口限制**：只能查询时间窗口内的热数据，超出范围的历史数据无法通过索引表快速定位
+ **查询模式受限**：为了保持索引表的简洁性和高性能，只能支持有限的查询模式
+ **存储成本**：需要额外的存储空间来维护索引映射关系

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753786317896-9ca5e3b5-e15c-4d89-b4f5-df06883b3dcc.svg)

#### 按增删改查划分的支持场景
基于业务操作类型和返回值特征，我们对查询场景进行了精确的分级支持定义：

**增（INSERT）操作**

+ ✅ **完全支持**：同时更新分表和索引表，保证数据一致性

**改（UPDATE）操作**

+ ✅ **完全支持**：同时更新分表和索引表，保证数据一致性

**查（SELECT）操作 - 分级支持（重点关注不带分表键的场景）**

_注：带分表键的查询是基础功能，必须完全支持，此处重点说明不带分表键的查询处理策略_

#### 不带分表键查询的分级处理
**第一级：单个对象返回（单点查询）**

+ **场景**：`WHERE user_id = 'U123'` 返回单个Order对象
+ **处理策略**：
    1. 先查询索引表获取分表键
    2. 若命中，直接查询对应分表
    3. 若未命中，执行有限制的全表扫描
+ **性能特点**：大部分情况下性能优异

**第二级：列表返回（多条数据）**

+ **场景**：`WHERE user_id = 'U123'` 返回List
+ **处理策略**：
    - 强制增加LIMIT限制（默认20条，可配置）
    - 打印警告日志记录此类查询
    - 执行全表扫描获取结果
+ **限制说明**：防止大批量数据查询影响系统性能

**第三级：聚合查询**

+ **场景**：`SELECT COUNT(*) WHERE user_id = 'U123'`
+ **处理策略**：支持基础聚合操作（COUNT、SUM、AVG等）
+ **性能特点**：聚合查询性能相对较高，可以接受

**第四级：受限的分页查询**

+ **滚动分页**：
    - ✅ **有限支持**：分页大小最多20条，超过报错
    - 不走索引表，直接全表扫描
    - 适用于数据浏览场景
+ **随机分页**：
    - ❌ **不支持**：基于OFFSET的分页会导致性能灾难

**完全不支持的查询类型**

+ ❌ **排序查询**：`WHERE user_id = 'U123' ORDER BY create_time`（无分表键的排序）
+ ❌ **复合条件查询**：`WHERE user_id = 'U123' AND phone = '138xxx'`（多个非分表键字段）
+ ❌ **多表查询**：涉及JOIN操作的查询
+ ❌ **子查询**：包含嵌套查询的复杂SQL
+ ❌ **范围查询**：`WHERE create_time BETWEEN '2024-01-01' AND '2024-01-31'`
+ ❌ **模糊查询**：`WHERE order_no LIKE 'O2024%'`

**历史数据查询（时间窗口外）**

+ **处理方式**：提示用户数据超出查询范围
+ **替代方案**：引导用户使用数据仓库或离线查询服务

### 不支持场景的替代方案
对于索引表不适合支持的查询场景，后续可以采用其他替代方案来解决非核心链路的**查询**请求：

1. **数据仓库查询**：复杂的历史数据分析和报表查询应通过数据仓库系统实现
2. **离线批处理**：大范围的数据导出和统计应使用离线批处理作业
3. **专门的查询服务**：为运营和客服等特殊场景提供独立的查询服务，可以有更宽松的资源限制

通过这套热数据索引表方案，我们在保持系统简洁性的同时，有效解决了分表后非分片键查询的性能问题，为绝大多数业务场景提供了高效的查询能力，同时为特殊场景提供了明确的替代方案。



# 分库分表调度引擎与AOP实现方案
## 整体架构：基于AOP的调度引擎设计
### AOP调度架构概述
为将复杂的分库分表逻辑对业务代码实现"零侵入"，我们设计了一套解耦的、基于AOP的实现方案。其核心设计原则是：**AOP只做"调度"，不做"执行"**。

**架构设计理念：**

+ **职责分离**：将AOP的功能拆分为多个职责单一、可独立测试的普通Java组件
+ **调度与执行分离**：AOP切面专注于拦截和调度，具体的业务逻辑由专门的处理器执行
+ **策略模式驱动**：通过策略模式实现不同场景下的灵活切换
+ **配置化管理**：所有策略切换通过配置中心动态控制，无需重启应用

**整体架构层次：**

```plain
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   AOP切面调度    │───▶│   执行引擎        │───▶│  策略处理器      │
│ ShardingAspect  │    │ExecutionEngine   │    │ Strategy Handler│
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   上下文封装     │    │   事务边界管理    │    │   HintManager   │
│ QueryContext    │    │ Transaction Mgmt │    │  路由控制       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753786531542-d2e8ab67-3247-4ec9-8a42-da75ef5476f0.svg)

### 核心组件关系设计
我们将AOP的功能拆分为多个职责单一、可独立测试的普通Java组件：

1. `ShardingIndexAspect`** (AOP切面 - 调度):**
    - **职责:** 唯一的`@Aspect`类，代码极简。负责拦截DAO方法，封装`QueryContext`上下文，然后转交给执行引擎。
2. `ShardingExecutionEngine`** (执行引擎):**
    - **职责:** 核心`@Service`，是**事务边界**的定义者。负责接收`QueryContext`，判断是读/写操作，并分发给相应的处理器。
3. `WriteOperationHandler`**(写操作处理器):**
+ **职责:** 在引擎定义的事务内，原子性地完成对业务数据的写入。
    - **信息补全策略 (针对索引表):** 对于不包含全量字段的`UPDATE`，处理器会采用“先更新主表，再根据ID重新`SELECT`最新数据”的策略，以获取完整的实体信息，用于写入**分区索引表**。
    - **双写期间的**`UPDATE`**特殊处理 (核心原则):**
        * **存在即更新:** `UPDATE`请求会先尝试更新新分片表。如果影响行数 > 0，则操作成功。
        * **不存在则记录:** 如果影响行数 = 0，说明此数据尚未被历史迁移任务同步。此时，处理器**不会执行**`INSERT`，而是将该记录的`ID`**写入一个独立的冲突日志表**（如 `migration_update_conflicts`）。此举极大简化了在线双写逻辑，将一致性问题统一交由后续的最终对齐脚本处理。
        * `INSERT`**操作:** `INSERT`请求会正常写入新、老两份表。
    - **1. StandardWriteStrategy (标准写策略):**
        * **角色:** 系统的**最终稳态**。
        * **行为:** 只向新架构（分片表 + 索引表）执行标准的写入操作。
        * **激活条件:** `sharding-architecture.dual-write=N` 或配置不存在时, 即默认策略。
    - **2. DualWriteTransitionalStrategy(双写过渡策略):**
        * **角色:** 迁移过渡期的**临时策略**。
        * **行为:** 遵循“先更新老表，再更新新表”的安全原则，在同一个事务内执行上述定义的特殊`UPDATE`和`INSERT`逻辑。
        * **激活条件:** `sharding-architecture.dual-write=Y`**。**
4. `ReadOperationHandler`** (读操作处理器):**
    - **职责:** 智能地路由所有读请求。
        * **1. ShardingReadStrategy(分片读策略):**
            + **角色:** 系统的**最终稳态**，性能最优。
            + **行为:** 高效地查询索引和分片表，不包含任何灰度逻辑。
            + **激活条件:** `sharding-architecture.read.path.mode=sharding` 或配置不存在时, 即默认策略。。
        * **2. GrayscaleReadTransitionalStrategy(灰度读过渡策略):**
            + **角色: 覆盖整个过渡期的临时策略**（**分片读策略的装饰者**）。
            + **行为:** 内部包含完整的灰度决策逻辑，一个策略即可覆盖从上线到固化的所有场景：
                - `percentage=0`**:** (安全上线/回滚) 行为等同于“只读老表”。
                - `percentage`**在1-99:** (灰度期) 按比例将流量路由到新表或老表。
                - `percentage=100`**:** (观察期) 全量读新表。
            + **激活条件:** `sharding-architecture.read.path.mode=grayscale_migration` 。

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753271144897-7ac226a5-deb8-4844-b1fa-ce7cbecf54b3.svg)

我们采用“代码即文档”的命名哲学和“装饰者”设计模式，构建了“两写两读”四种策略，并通过Spring的`@ConditionalOnProperty`注解实现动态切换。

```java
/**
 * 写操作处理器的统一接口。
 */
interface IWriteOperationHandler {
    Object handle(QueryContext context) throws Throwable;
}

/**
 * 读操作处理器的统一接口。
 */
interface IReadOperationHandler {
    Object handle(QueryContext context) throws Throwable;
}
```

```java
@Component
@ConditionalOnProperty(name = "sharding-architecture.dual-write", havingValue = "N", matchIfMissing = true)
class StandardWriteStrategy implements IWriteOperationHandler {...}

@Component
@ConditionalOnProperty(name = "sharding-architecture.dual-write", havingValue = "Y")
class DualWriteTransitionalStrategy implements IWriteOperationHandler {...}

```

```java
@Component
@ConditionalOnProperty(name = "sharding-architecture.read.path.mode", havingValue = "sharding", matchIfMissing = true)
class ShardingReadStrategy implements IReadOperationHandler {...}

@Component
@ConditionalOnProperty(name = "sharding-architecture.read.path.mode", havingValue = "grayscale_migration")
class GrayscaleReadTransitionalStrategy implements IReadOperationHandler {...}
```

```java
...
// 写新表(已被sharing接管数据源和分片规则, 默认写分表)
tableDao.save(...)
...
try (Closeable ignored = HintManager.getInstance()) {
    // 强制下一条SQL语句路由到名为't_order'的物理表，绕过所有分片规则
    HintManager.getInstance().setWriteRouteOnly(); // ShardingSphere 5.x 推荐用法
    // 已经强制不走分片规则, 不会为逻辑表按照分片规则来分片一个物理分表, 会直接到原始表
    tableDao.save(...); 
}
```

## 事务传播行为与原子性保障
### 事务边界设计
`ShardingExecutionEngine`的执行入口使用`@Transactional(propagation = Propagation.REQUIRED)`来确保"主表-索引表"复合操作的原子性。

**事务传播机制设计：**

```java
/**
 * 分片执行引擎 - 事务边界管理器
 * 负责统一管理分库分表操作的事务边界
 */
@Service
@Transactional(propagation = Propagation.REQUIRED)
public class ShardingExecutionEngine implements IShardingExecutionEngine {
    
    /**
     * 核心执行入口 - 事务边界定义点
     * @param context 查询上下文
     * @return 执行结果
     */
    public Object execute(QueryContext context) throws Throwable {
        // TODO
    }
}
```

**两种事务场景处理：**

1. **场景一：外部已存在大事务**
    - 当切面拦截的方法被`@Transactional`注解的Service方法调用时
    - 执行引擎会**加入（join）**到外部事务中
    - 主表和索引表的写入成为大事务的一部分，共同提交或回滚
2. **场景二：外部无事务（默认autocommit模式）**
    - 当切面拦截的方法被无`@Transactional`注解的方法调用时
    - 执行引擎会**创建全新事务**
    - 将"主表写入 + 索引表写入"包裹成新的原子单元

### 事务传播级别规范
为防止被调用的DAO方法使用错误的事务传播级别破坏设计的事务边界，我们制定严格的开发规范：

**禁用规则：**

+ **严禁使用**`@Transactional(propagation = REQUIRES_NEW)`
+ **DAO/Mapper层方法**不得定义独立的事务边界
+ **所有事务控制**统一由执行引擎管理

**自动化架构守护：**

```java
/**
 * 事务传播级别检查器
 * 在CI/CD流程中自动检查代码规范
 */
@Component
public class TransactionPropagationValidator {
    
    /**
     * 检查DAO层方法的事务注解
     * 确保不使用REQUIRES_NEW传播级别
     */
    public void validateDaoTransactionAnnotations() {
        // 扫描所有@Repository和@Mapper注解的类
        // 检查方法上的@Transactional注解
        // 发现REQUIRES_NEW时抛出异常，阻止构建
    }
}
```

通过这套完整的调度引擎与AOP实现方案，我们实现了：

1. **零侵入的业务集成**：通过AOP切面，业务代码无需任何修改
2. **灵活的策略切换**：通过配置中心动态控制不同阶段的策略
3. **强一致性保障**：通过事务管理确保主表和索引表的数据一致性
4. **高性能的查询路由**：通过智能路由决策实现最优的查询性能
5. **安全的迁移过程**：通过双写和灰度策略确保平滑的数据迁移

整个方案在保证系统稳定性的同时，为分库分表的复杂场景提供了完整的解决方案

---

# 上线与迁移：存量增量分离的数据同步策略
## 数据同步整体架构
### 存量增量分离模式
在分库分表的上线迁移过程中，我们采用**存量增量分离**的数据同步策略，将数据迁移问题分解为两个相对独立的子问题：

**存量数据处理：历史数据的一次性批量迁移**

+ **定义**：系统上线前已存在的所有历史数据
+ **特点**：数据量大、时间跨度长、数据相对稳定
+ **处理方式**：通过自研迁移脚本进行批量迁移
+ **时间要求**：可以在业务低峰期进行，允许较长的处理时间

**增量数据处理：新产生数据的实时双写同步**

+ **定义**：迁移过程中新产生的业务数据
+ **特点**：数据量相对较小、实时性要求高、需要保证业务连续性
+ **处理方式**：通过双写策略实现新旧表的同步写入
+ **时间要求**：必须实时处理，不能影响正常业务操作

**数据一致性保障：两种模式下的一致性策略**

+ **存量数据一致性**：通过迁移后的实时校验确保数据完整性
+ **增量数据一致性**：通过双写机制和冲突检测确保数据同步
+ **最终一致性**：通过全量校验脚本确保新旧表数据完全一致

### 核心技术挑战
**单一DAO双路由问题：如何用一套DAO同时访问新旧表**

这是整个迁移方案面临的最核心技术挑战。在迁移期间，我们需要：

+ 对于存量数据：从旧表读取，向新表写入
+ 对于增量数据：同时向新旧表写入，根据策略从不同表读取
+ 对于业务代码：保持完全透明，无需任何修改

**ShardingSphere配置冲突：逻辑表配置与物理表访问的矛盾**

ShardingSphere的配置文件中，逻辑表`t_order`被配置为指向新的分片表：

```yaml
rules:
  sharding:
    tables:
      t_order:
        actual-data-nodes: ds0.t_order_$->{0..127}
        table-strategy:
          standard:
            sharding-column: id
            sharding-algorithm-name: hash_mod
```

但在迁移期间，我们需要访问物理上的旧表`t_order`，这就产生了配置冲突。

**迁移期间数据完整性：存量迁移与增量双写的协调**

在存量数据迁移过程中，业务系统仍在正常运行，新产生的数据需要通过双写机制同步。这就要求：

+ 存量迁移脚本不能影响正常业务操作
+ 双写机制不能与迁移脚本产生数据冲突
+ 需要有机制检测和处理迁移与双写的竞态条件

## 解决方案：HintManager 
`ShardingSphere-JDBC`提供的`HintManager`是一个**线程级**的API，允许我们在业务代码中，通过编程方式下达一个**优先级高于所有YAML配置规则**的“指令”，在当前线程中强制SQL的路由行为。

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753787933302-38315ef8-f0ae-4005-a507-4adfc9f2c5f8.svg)



## 双写执行逻辑
我们采用"先写旧表，再写新表"的顺序，这样设计的原因：

    1. **数据安全性**：旧表是当前生产环境的数据源，优先保证其数据完整性
    2. **回滚便利性**：如果新表写入失败，旧表数据仍然完整，系统可以继续运行
    3. **一致性检测**：通过对比新旧表的写入结果，可以及时发现数据不一致

**事务边界管理：双写操作的原子性保障**

双写操作需要在同一个事务中完成，确保要么全部成功，要么全部失败

**性能影响评估：双写对系统性能的影响分析**

双写策略会对系统性能产生一定影响：

1. **写入延迟增加**：需要写入两张表，理论上延迟会增加一倍
2. **数据库连接消耗**：需要更多的数据库连接来处理双写操作
3. **事务时间延长**：事务持续时间增加，可能影响并发性能

### 双写冲突处理
在双写过程中，可能会出现各种冲突情况，需要针对不同的操作类型采用不同的处理策略：

**INSERT操作处理：新增数据的双写同步**

INSERT操作是最简单的双写场景，通常不会出现冲突

**UPDATE操作处理：更新数据的双写同步**

UPDATE操作是最复杂的双写场景，需要处理各种边界情况

~~**DELETE操作处理：删除数据的双写同步**~~

~~DELETE操作相对简单，但也需要处理数据不存在的情况~~

**冲突检测与记录：双写不一致的识别与日志记录**

为了系统地管理双写过程中的各种冲突，我们设计了统一的冲突检测和记录机制：

```java
/**
 * 双写冲突检测器
 * 统一检测和记录双写过程中的各种冲突情况
 */
@Component
public class DualWriteConflictDetector {

    private static final Logger log = LoggerFactory.getLogger(DualWriteConflictDetector.class);

    /** 冲突记录DAO */
    @Autowired
    private IConflictLogDao conflictLogDao;

    /** 告警管理器 */
    @Autowired
    private IAlertManager alertManager;

    /**
     * 检测并记录冲突
     * @param context 查询上下文
     * @param conflictType 冲突类型
     * @param description 冲突描述
     * @param severity 严重程度
     */
    public void detectAndRecord(QueryContext context, String conflictType,
                               String description, ConflictSeverity severity) {

        // 记录冲突日志
    }

    /**
     * 发送冲突告警
     */
    private void sendConflictAlert(ConflictLogEntity conflict) {
    }
}
```

通过这套完整的双写冲突处理机制，我们可以：

1. **及时发现问题**：通过实时冲突检测，快速识别数据不一致
2. **分类处理冲突**：根据冲突类型和严重程度采用不同的处理策略
3. **保证业务连续性**：即使出现冲突，也不会中断正常的业务流程
4. **提供修复依据**：详细的冲突日志为后续的数据修复提供依据

这种设计确保了在复杂的双写场景下，系统仍能保持高可用性和数据一致性。



## 存量数据迁移工程
在执行迁移前，我们必须面对一个核心挑战：**历史数据的来源不是单一的，而是根据不同历史归档策略，呈现出三种不同的形态**（巨型单表、按月归档全表、按月归档大字段）。这意味着我们不能用一个简单的脚本来处理所有情况。

### 技术选型考量：DataX vs. 自研
在选择迁移工具时，我们对业界流行的开源数据同步工具DataX与完全自研方案进行了对比。

+ **DataX方案评估:**
    - **优势:** DataX是阿里巴巴开源的成熟数据同步工具，性能稳定。其核心的`splitPk`功能在处理海量单表（我们的场景A）时，能自动分片、并发抽取，极大提升迁移效率。
    - **劣势与挑战:**
        1. **复杂转换能力缺失:** DataX被设计为纯粹的数据搬运工，其转换（Transform）能力很弱。对于我们最复杂的“大字段归档”场景，它无法原生支持需要聚合的逻辑。
        2. **写入逻辑不匹配:** 其标准的`mysqlwriter`插件只能向单张目标表写入，不具备根据源数据计算并写入到不同分片表（`t_order_N`）的能力。
        3. **流程编排笨重:** 对于“按月归档”场景，需要外部脚本循环调用DataX作业，整个流程的原子性和状态管理会变得非常复杂。
        4. **性能调优不可控：** 黑盒, 无法精准地控制读写批次、并发数和限流，在速度和系统影响间找到最佳平衡。
    - **结论:** 若要使用DataX，我们必须为其开发高度定制的`Writer`插件，并用外部脚本包裹复杂的`Reader`和编排逻辑。这不仅开发成本高，也违背了使用成熟工具简化问题的初衷，使其“黑盒”特性带来的风险远大于收益。
+ **自研方案评估:**
    - **优势:** 核心优势在于**稳定可控**。无论是复杂的字段转换、灵活的分片写入，还是精细化的错误处理、重试、限流与监控，所有逻辑都在我们自己的代码中，清晰、透明、易于调试和扩展。
    - **劣势:** 需要投入一定的开发资源，从零构建。

### 最终决策：研发高可控的自定义迁移工具
基于以上对比，为了确保迁移过程的绝对稳定与可控，我们最终决定**不依赖任何第三方数据同步工具，而是自主研发一个独立的、高可用的、可配置的Java迁移应用程序**。

自研的核心优势在于：

+ **逻辑完全可控：** 尤其是处理“大字段归档”这种需要应用层聚合的复杂逻辑，自己写的代码最清晰、最可靠。
+ **错误处理精细化：** 我们可以定义任意粒度的错误处理、重试和死信队列机制。
+ **性能调优灵活：** 可以精准地控制读写批次、并发数和限流，在速度和系统影响间找到最佳平衡。
+ **无外部依赖：** 整个迁移方案的技术栈与我们现有应用保持一致，降低了运维复杂度。

### 最终迁移引擎：一个自愈合的分布式并行模型
基于我们对迁移任务高并发、高容错、高可控的核心要求，我们最终设计了一套“**基于三优级决策的并发执行模型**”。该模型不依赖于外部的复杂调度器或全局规划者，而是赋予每一个调度作业（Worker）一个相同的、强大的决策流程，使其能够在一个无中心化的集群中高效、安全地协同工作。

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753613014125-58bcc76e-2b02-4374-9abc-85cd7450543b.svg)

#### 1. 任务窗口的四态生命周期
任务窗口的四态生命周期

我们定义了四个核心状态来精确描述一个任务窗口从诞生到完成的全过程。

+ `PROCESSING`** (处理中):** 任务的“活跃”状态。这是一个临时锁定状态，表示该任务已被某个Worker认领并正在处理。一个任务在被创建时，其初始状态就是`PROCESSING`。
+ `WAITING`** (等待/暂停):** 任务的“静止”状态。它的唯一来源是`PROCESSING`状态的任务因为调度时间耗尽而正常暂停。它代表一个“未竟的事业”，等待下一个调度周期的Worker来继续。
+ `FAILED`** (失败):** 任务的“硬错误”状态。当Worker在处理过程中遇到不可恢复的程序或数据错误时，会将任务置于此状态。它代表一个需要被优先修复的“坏区”，并包含重试计数器，**达到上限后需要人工介入**。
+ `SUCCEEDED`** (成功):** 任务的“最终完成”状态。当Worker完整地处理完一个窗口的数据后，将其置于此状态。它是一个稳定的、不可逆的“里程碑”，是后续“开拓者”创建新任务的基石。

****

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753615932642-62314c8e-41f9-4f40-995a-8114644cec3a.svg)

#### 2. Worker的三优级决策流程
每一个调度作业（Worker）都严格遵循以下优先级顺序来寻找并执行工作，这确保了系统始终将资源用于最关键的地方。

    - **第一优先级：救援队 (处理“假死”的PROCESSING任务)**
        * **职责： **主动寻找并接管那些可能因为Worker进程崩溃而被遗弃的任务。
        * **动作： **执行一个原子性的UPDATE，尝试“抢占”一个长时间没有“心跳”（即update_datetime长时间未更新）的PROCESSING任务。
        * **流转： **成功则接手处理；失败则进入下一优先级。

```sql
        UPDATE migration_window_tasks
        SET 
            status = 'PROCESSING', 
            worker_id = 'rescue_worker_id',
            -- 关键：救援也算一次重试，防止一个任务反复导致崩溃
            retry_count = retry_count + 1 
        WHERE 
            status = 'PROCESSING' 
            AND update_datetime < NOW() - INTERVAL 5 MINUTE -- 例如，超过5分钟没心跳就视为假死
            AND retry_count < 5 -- 同样需要熔断机制
        LIMIT 1;
```

    - **第一优先级：维修工 (找坏的重新处理)**
        * **职责:** 优先修复已知的系统故障，保证数据完整性。
        * **动作:** 执行一个原子性的`UPDATE`，尝试“抢占”一个`status = 'FAILED'`的任务。这是通过`UPDATE ... WHERE status = 'FAILED' LIMIT 1`实现的，利用数据库的原子性保证在并发场景下只有一个Worker能成功。
        * **流转:** 成功则将任务状态变为`PROCESSING`并开始处理；失败（没有找到`FAILED`任务）则立即进入下一优先级。
    - **第二优先级：接力者 (找等待的继续处理)**
        * **职责:** 继续之前因时间耗尽而暂停的任务，保证迁移的持续推进。
        * **动作:** 执行一个原子性的`UPDATE`，尝试“抢占”一个`status = 'WAITING'`的任务。
        * **流转:** 成功则将任务状态变为`PROCESSING`并从检查点继续处理；失败（没有找到`WAITING`任务）则立即进入下一优先级。
    - **第三优先级：开拓者 (找最后的添加处理)**
        * **职责:** 在确认没有旧工作可做之后，安全地开辟新的任务疆域，为整个集群提供新的工作源。
        * **动作:**
            1. ~~**开启事务，加锁排队:**~~~~ 执行~~`~~SELECT ... FROM migration_window_tasks ORDER BY end_id DESC LIMIT 1 FOR UPDATE~~`~~。通过对物理上的~~~~**最后一条记录**~~~~加行锁，确保了所有想成为“开拓者”的Worker在此处安全、串行地排队，杜绝了并发创建新任务的任何风险。~~
        3. 采用分布式锁或者其他方式代替for update锁
            1. **无条件创建:** 一旦获取到锁，**开拓者不关心它锁定的最后一条记录是什么状态**。它只从中读取`end_id`，然后计算并`INSERT`一条全新的任务记录，**状态直接设为**`PROCESSING`，并认领为自己的任务。
        4. **提交事务，立即执行:** 事务提交后，行锁被释放，该Worker立即带着它刚刚创建的新任务，进入核心处理循环。
        5. **流转:** 这个设计完美地实现了“**开拓串行，执行并行**”。开拓者的行锁只锁定最后一条记录，完全不影响其他“执行者”并发处理表中其他状态的记录。同时，它“无条件创建”的逻辑保证了迁移进度永远向前，将修复和接力的工作完全交给后续的调度来弥补，实现了极致的关注点分离。

#### 3. 核心处理循环与断点续传
无论是通过哪个优先级获取到了任务，Worker都将进入统一的、包含“窗口内检查点”的核心处理循环。

+ **时间控制:** 循环体内会持续检查当前作业的运行时间，一旦接近调度上限，会安全地将任务状态从`PROCESSING`更新为`WAITING`，并确保最新的检查点ID已保存，然后优雅退出。
+ **批次处理:** 通过`checkpoint_id`，循环地从源表读取小批量数据 (`WHERE id > checkpoint_id`)。
+ **密集存档:** 每成功处理完一个批次，就立即`UPDATE`该任务的`checkpoint_id`，实现高频、低成本的“自动存档”，将故障恢复的损失降到最低。
+ **终态变更:** 当循环发现已无数据可读，则将任务状态更新为`SUCCEEDED`；若中途遇到不可恢复的错误，则更新为`FAILED`。

**迁移校验一体化工具设计**

该工具的设计原则是：**在线双写逻辑极度轻量，后台迁移工具承担全部的一致性校验职责。**

1. **工作模式: “删-插-查-比” (Delete-Insert-Select-Compare)**
    - 工具以批次（batch）为单位，循环执行以下高度严谨的原子性操作，直至处理完所有历史数据：
    - **a. 清空窗口 (**`DELETE`**):** 在处理一个批次前，工具首先执行 `DELETE FROM new_table WHERE id IN (...)`，清空该数据窗口在新表中的所有记录，为后续操作提供一个“干净”的环境(事务外)。
    - **b. 权威插入 (**`INSERT`**):** 从**老表**中读取该窗口的权威数据，然后执行一次干净的批量 `INSERT` 将其写入新表。
    - **c. 立即回查 (**`SELECT`**):** `INSERT` 刚一完成，工具立即根据同一批ID，再从**新表**中将数据重新查询出来。
    - **d. 数量校验 (COUNT CHECK) : ** 立刻比较“从老表读出的记录数”和“从新表回查的记录数”。这两个数字必须严格相等。如果不等，说明在 INSERT 或 SELECT 环节发生了严重的数据丢失或异常，这是一个致命错误，必须立即回滚当前事务，并触发高级别告警，需要人工介入调查。
    - **e. 内存比对，记录冲突 (**`COMPARE & LOG`**):** 工具在内存中对“源头真理”（来自老表）和“写入结果”（来自新表）进行全字段比对。如果发现不一致，只说明一个情况：在`b`和`c`之间的微秒级窗口内，一个在线双写`UPDATE`修改了数据。这就是最精确的竞态冲突。工具会将该冲突`ID`**写入独立的**`migration_conflicts`**冲突日志表**。
2. **配置驱动与读取策略：** 与原方案一致，支持多种数据源形态。
3. **健壮的非功能性要求：** 必须具备断点续传、资源可控、详尽监控与日志等特性。

# **数据**最终一致性保障 
## 核心挑战：双写与迁移的竞态冲突
### 双写前历史数据的UPDATE冲突问题
**问题描述：**  
	在双写策略启动后，系统会同时向新旧两张表写入数据。但对于双写开始前就存在的历史数据，当业务发起UPDATE操作时，会出现以下问题：

+ **旧表UPDATE成功**：历史数据在旧表中存在，UPDATE操作正常执行
+ **新表UPDATE失败**：由于该数据尚未被迁移脚本同步到新表，UPDATE操作影响行数为0
+ **数据状态不一致**：旧表数据被更新，新表数据不存在，造成严重的数据不一致

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753847636321-47a84ba4-74a5-4fd2-9f42-c5e31b067ef8.svg)

### 迁移脚本与增量双写的竞态条件问题
**问题描述：**  
	在迁移脚本执行过程中，与增量双写操作存在微妙的时间窗口竞态：

1. **迁移脚本读取**：从旧表读取一批数据（如1000条）
2. **增量更新发生**：在迁移脚本写入新表前，业务发起了对其中某条数据的UPDATE
3. **迁移脚本写入**：迁移脚本将读取到的"旧状态"数据写入新表
4. **数据被覆盖**：增量更新的"新状态"被迁移脚本的"旧状态"覆盖

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753847989381-08f74398-4099-4eb6-8bbb-47e8d9f2aa23.svg)

## 冲突检测与记录机制
### 冲突表设计与建设
通过对上述两个场景记录冲突

为了系统性地管理和解决上述冲突，我们设计专门的冲突记录表：

```sql
-- 迁移冲突记录表
CREATE TABLE `{table_name}_migration_conflicts` (
  `id` varchar(50) NOT NULL COMMENT '冲突id',
  `status` VARCHAR(16) DEFAULT 'PENDING' COMMENT '处理状态：PENDING|RESOLVED|FAILED',
  `created_by` varchar(20) NOT NULL DEFAULT 'SYS' COMMENT '创建人',
  `updated_by` varchar(20) DEFAULT NULL COMMENT '更新人',
  `create_datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_datetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

  PRIMARY KEY (`id`),
) ENGINE=InnoDB COMMENT='迁移过程冲突记录表';
```

### 冲突记录的触发时机
+ **双写UPDATE冲突记录: **在双写策略的UPDATE操作中，当新表更新影响行数为0时触发：
+ **迁移过程竞态冲突记录: **在迁移脚本的数据比对过程中，发现内容不一致时触发：

### 冲突数据的批量修复流程
    - 迁移完成后，通过专门的修复脚本处理所有冲突数据
    - 在冲突解决过程中，我们始终以旧表数据为准，无条件覆盖新表数据
    - **幂等性设计原则：**
        1. **可重复执行**：修复脚本可以多次运行，不会产生副作用
        2. **状态追踪**：通过冲突表状态字段追踪处理进度
        3. **断点续传**：支持从中断点继续执行

### 并行校验脚本设计
在迁移过程中，同时运行独立的校验脚本，对已完成迁移的数据进行验

证在迁移N月数据时，同时校验N-1月的数据，确保已迁移数据的持续一致性。

![](https://cdn.nlark.com/yuque/0/2025/svg/2380044/1753848567308-814ea5aa-c8bb-4cd1-beca-1e2f4004e217.svg)

